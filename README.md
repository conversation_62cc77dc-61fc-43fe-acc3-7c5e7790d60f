## Http Swoole Server

shuipf/swoole 是基于Swoole扩展实现的常驻内存、协程非阻塞 IO 特性。

## 单元测试

`docker run -it --rm -v ${PWD}:/app php:8.0-cli sh -c "cd /app && php ./vendor/bin/phpunit"`


## 开始使用

[think-swoole](https://github.com/top-think/think-swoole)

[Swoole手册](https://wiki.swoole.com/wiki/index/prid-1)

[ThinkPHP完整手册](https://doc.thinkphp.cn/v8_0/preface.html)

[Swoole进程关系](https://blog.csdn.net/t2337025/article/details/90042462)

## 使用方法


直接在命令行下启动HTTP服务端。

~~~
php think swoole
~~~

启动完成后，默认会在0.0.0.0:8080启动一个HTTP Server，可以直接访问当前的应用。

swoole的相关参数可以在`config/swoole.php`里面配置（具体参考配置文件内容）。

如果需要使用守护进程方式运行，可以配置

~~~
'options'   =>  [
    'daemonize' =>  true
]
~~~

支持的操作包括
~~~
php think swoole
~~~

## websocket

> 新增路由调度的方式，方便实现多个websocket服务

## 配置

```
swoole.websocket.route = true 时开启
```

## 路由定义
```php
Route::get('path1','controller/action1');
Route::get('path2','controller/action2');
```

## 控制器

```php
use \think\swoole\Websocket;
use \think\swoole\websocket\Event;
use \Swoole\WebSocket\Frame;
use \think\swoole\websocket\Room;

class Controller {

    public function action1(){//不可以在这里注入websocket对象
    
        return \think\swoole\helper\websocket()
            ->onOpen(...)
            ->onMessage(function(Websocket $websocket, Frame $frame){ //只可在事件响应这里注入websocket对象
                //...
                $websocket->join('room_key'); //将当前连接加入到某个room，后续可以向该room发送消息 这个room里的都可以收到
                //比如room_key可以直接使用这个用户的id，然后其他地方需要给某个用户发送消息，直接向这个room发送消息即可
                //...
                $websocket->push('message'); //给当前连接发送消息
                //...
                $websocket->emit('event_name', 'message'); //给当前连接发送事件
                //...
                $websocket->to('room_key')->push('message'); //给指定room的所有连接发送消息 在http请求的控制器中也可以注入Websocket对象这样发消息
                //...
            })
            ->onClose(...);
    }
    
    public function action2(){
    
        return \think\swoole\helper\websocket()
            ->onOpen(...)
            ->onMessage(function(Websocket $websocket, Frame $frame){
               //...
            })
            ->onClose(...);
    }
}
```

## 流式输出
```php
class Controller {
    public function action(){
        return \think\swoole\helper\iterator(value(function(){
            foreach(range(1,10) as $i)
                yield $i;
                sleep(1);//模拟等待
            }
        }));
    }
}
```