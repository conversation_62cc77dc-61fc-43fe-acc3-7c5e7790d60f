{"name": "shuipf/swoole", "description": "swoole", "authors": [{"name": "水平凡", "email": "<EMAIL>"}], "type": "library", "license": "Apache-2.0", "require": {"php": "^8.0", "ext-json": "*", "ext-swoole": "^4.0|^5.0|^6.0", "nette/php-generator": "^4.0", "open-smf/connection-pool": ">=1.0", "stechstudio/backoff": "^1.2", "symfony/finder": ">=4.3", "topthink/framework": "^6.0|^8.0", "swoole/ide-helper": "^5.0", "symfony/process": ">=4.2"}, "require-dev": {"topthink/think-tracing": "^1.0", "shuipf/queue": "^1.0"}, "autoload": {"psr-4": {"think\\swoole\\": "src"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"tests\\": "tests/"}}, "extra": {"think": {"services": ["think\\swoole\\Service"], "config": {"swoole": "src/config/swoole.php"}}}, "config": {"preferred-install": "dist", "sort-packages": true, "platform-check": false, "platform": {"ext-swoole": "5.0.0", "ext-fileinfo": "1.0.4"}, "allow-plugins": {"pestphp/pest-plugin": true}}, "scripts": {}, "repositories": {"shuipf/packages": {"type": "composer", "url": "https://packages.shuipf.com/"}}}