# HTTP服务使用指南

## 功能概述

HTTP服务是基于Swoole HTTP Server实现的高性能Web服务器，支持协程、长连接、WebSocket升级等特性。该服务提供了完整的HTTP/1.1协议支持，包括请求处理、响应生成、中间件支持、静态文件服务等功能，适用于构建高并发的Web应用和API服务。

## 适用场景

- 高并发Web应用
- RESTful API服务
- 微服务架构
- 实时Web应用
- 静态文件服务
- 代理和网关服务
- WebSocket应用

## 配置说明

### 基础配置

在 `config/swoole.php` 文件中配置HTTP服务：

```php
'http' => [
    'enable' => true,                       // 是否启用HTTP服务
    'host' => '0.0.0.0',                   // 监听地址
    'port' => 8080,                        // 监听端口
    'worker_num' => 4,                     // 工作进程数
    
    // Swoole服务器选项
    'options' => [
        'reactor_num' => 2,                 // Reactor线程数
        'worker_num' => 4,                  // Worker进程数
        'max_request' => 10000,             // 最大请求数
        'max_conn' => 10000,                // 最大连接数
        'task_worker_num' => 2,             // Task进程数
        'task_max_request' => 5000,         // Task最大请求数
        'enable_coroutine' => true,         // 启用协程
        'max_coroutine' => 100000,          // 最大协程数
        
        // 缓冲区设置
        'package_max_length' => 2 * 1024 * 1024,  // 2MB
        'buffer_output_size' => 2 * 1024 * 1024,  // 2MB
        
        // 超时设置
        'socket_buffer_size' => 128 * 1024 * 1024, // 128MB
        'heartbeat_check_interval' => 60,   // 心跳检测间隔
        'heartbeat_idle_time' => 600,       // 连接最大空闲时间
        
        // 日志设置
        'log_file' => runtime_path('logs/swoole.log'),
        'log_level' => SWOOLE_LOG_INFO,
        
        // 守护进程
        'daemonize' => false,               // 是否守护进程化
        'pid_file' => runtime_path('swoole.pid'),
    ],
],
```

### 环境变量配置

在 `.env` 文件中配置HTTP服务参数：

```bash
# HTTP服务配置
SWOOLE_HOST=0.0.0.0
SWOOLE_PORT=8080
SWOOLE_WORKER_NUM=4

# 性能配置
SWOOLE_MAX_REQUEST=10000
SWOOLE_MAX_CONN=10000
SWOOLE_MAX_COROUTINE=100000

# 开发环境配置
SWOOLE_HOT_UPDATE=true
SWOOLE_DAEMONIZE=false
```

### 配置项详解

#### 基础配置项

- `enable`: 是否启用HTTP服务
- `host`: 服务监听地址，0.0.0.0表示监听所有网卡
- `port`: 服务监听端口
- `worker_num`: Worker进程数量，建议设置为CPU核心数

#### 性能配置项

- `reactor_num`: Reactor线程数，负责网络IO
- `max_request`: Worker进程最大处理请求数，达到后重启进程
- `max_conn`: 最大并发连接数
- `max_coroutine`: 单个Worker进程最大协程数

## 使用方法

### 1. 启动HTTP服务

#### 基本启动

```bash
# 启动HTTP服务
php think swoole

# 指定配置启动
php think swoole --host=127.0.0.1 --port=9000

# 守护进程模式启动
php think swoole -d

# 重启服务
php think swoole restart

# 停止服务
php think swoole stop

# 重新加载
php think swoole reload
```

#### 服务状态管理

```bash
# 查看服务状态
php think swoole status

# 查看进程信息
ps aux | grep swoole

# 查看端口占用
netstat -tlnp | grep 8080
```

### 2. 创建HTTP控制器

#### 基础控制器

```php
<?php
// app/controller/Index.php

namespace app\controller;

use think\Request;
use think\Response;

class Index
{
    /**
     * 首页
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        return json([
            'code' => 200,
            'msg' => 'Hello Swoole HTTP Server',
            'data' => [
                'server_time' => date('Y-m-d H:i:s'),
                'request_id' => uniqid(),
                'client_ip' => $request->ip(),
                'user_agent' => $request->header('user-agent'),
            ]
        ]);
    }
    
    /**
     * 健康检查
     * @return Response
     */
    public function health()
    {
        return json([
            'status' => 'ok',
            'timestamp' => time(),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
        ]);
    }
}
```

#### API控制器示例

```php
<?php
// app/controller/api/User.php

namespace app\controller\api;

use think\Request;
use think\Response;
use think\facade\Validate;
use app\service\UserService;

class User
{
    /**
     * @var UserService 用户服务
     */
    protected $userService;
    
    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }
    
    /**
     * 获取用户列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        $page = $request->param('page', 1, 'intval');
        $limit = $request->param('limit', 20, 'intval');
        $keyword = $request->param('keyword', '');
        
        try {
            $result = $this->userService->getUserList($page, $limit, $keyword);
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $result
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取用户详情
     * @param Request $request
     * @param int $id 用户ID
     * @return Response
     */
    public function read(Request $request, int $id)
    {
        try {
            $user = $this->userService->getUserById($id);
            
            if (!$user) {
                return json(['code' => 404, 'msg' => '用户不存在']);
            }
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $user
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 创建用户
     * @param Request $request
     * @return Response
     */
    public function save(Request $request)
    {
        // 参数验证
        $validate = Validate::rule([
            'username' => 'require|alphaNum|length:3,20',
            'email' => 'require|email',
            'password' => 'require|length:6,20',
        ]);
        
        if (!$validate->check($request->param())) {
            return json([
                'code' => 400,
                'msg' => '参数验证失败',
                'errors' => $validate->getError()
            ]);
        }
        
        try {
            $userData = $request->only(['username', 'email', 'password']);
            $userId = $this->userService->createUser($userData);
            
            return json([
                'code' => 200,
                'msg' => '创建成功',
                'data' => ['user_id' => $userId]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '创建失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 更新用户
     * @param Request $request
     * @param int $id 用户ID
     * @return Response
     */
    public function update(Request $request, int $id)
    {
        try {
            $userData = $request->only(['username', 'email']);
            $result = $this->userService->updateUser($id, $userData);
            
            if (!$result) {
                return json(['code' => 404, 'msg' => '用户不存在']);
            }
            
            return json([
                'code' => 200,
                'msg' => '更新成功'
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '更新失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 删除用户
     * @param Request $request
     * @param int $id 用户ID
     * @return Response
     */
    public function delete(Request $request, int $id)
    {
        try {
            $result = $this->userService->deleteUser($id);
            
            if (!$result) {
                return json(['code' => 404, 'msg' => '用户不存在']);
            }
            
            return json([
                'code' => 200,
                'msg' => '删除成功'
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '删除失败: ' . $e->getMessage()
            ]);
        }
    }
}
```

### 3. 文件上传处理

```php
<?php
// app/controller/Upload.php

namespace app\controller;

use think\Request;
use think\Response;
use think\facade\Filesystem;

class Upload
{
    /**
     * 单文件上传
     * @param Request $request
     * @return Response
     */
    public function single(Request $request)
    {
        $file = $request->file('file');
        
        if (!$file) {
            return json(['code' => 400, 'msg' => '请选择要上传的文件']);
        }
        
        try {
            // 验证文件
            $validate = [
                'size' => 10 * 1024 * 1024,  // 10MB
                'ext' => 'jpg,jpeg,png,gif,pdf,doc,docx',
            ];
            
            if (!$file->check($validate)) {
                return json([
                    'code' => 400,
                    'msg' => '文件验证失败: ' . $file->getError()
                ]);
            }
            
            // 保存文件
            $savePath = Filesystem::disk('public')->putFile('uploads', $file);
            
            if (!$savePath) {
                return json(['code' => 500, 'msg' => '文件保存失败']);
            }
            
            return json([
                'code' => 200,
                'msg' => '上传成功',
                'data' => [
                    'filename' => $file->getOriginalName(),
                    'path' => $savePath,
                    'url' => '/storage/' . $savePath,
                    'size' => $file->getSize(),
                    'mime' => $file->getMimeType(),
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '上传失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 多文件上传
     * @param Request $request
     * @return Response
     */
    public function multiple(Request $request)
    {
        $files = $request->file('files');
        
        if (!$files || !is_array($files)) {
            return json(['code' => 400, 'msg' => '请选择要上传的文件']);
        }
        
        $results = [];
        $errors = [];
        
        foreach ($files as $index => $file) {
            try {
                // 验证文件
                $validate = [
                    'size' => 5 * 1024 * 1024,  // 5MB
                    'ext' => 'jpg,jpeg,png,gif',
                ];
                
                if (!$file->check($validate)) {
                    $errors[$index] = $file->getError();
                    continue;
                }
                
                // 保存文件
                $savePath = Filesystem::disk('public')->putFile('uploads', $file);
                
                if ($savePath) {
                    $results[$index] = [
                        'filename' => $file->getOriginalName(),
                        'path' => $savePath,
                        'url' => '/storage/' . $savePath,
                        'size' => $file->getSize(),
                    ];
                } else {
                    $errors[$index] = '文件保存失败';
                }
                
            } catch (\Exception $e) {
                $errors[$index] = $e->getMessage();
            }
        }
        
        return json([
            'code' => 200,
            'msg' => '上传完成',
            'data' => [
                'success' => $results,
                'errors' => $errors,
                'total' => count($files),
                'success_count' => count($results),
                'error_count' => count($errors),
            ]
        ]);
    }
}
```

### 4. 流式响应处理

```php
<?php
// app/controller/Stream.php

namespace app\controller;

use think\Request;
use think\Response;

class Stream
{
    /**
     * 大文件下载
     * @param Request $request
     * @return Response
     */
    public function download(Request $request)
    {
        $filename = $request->param('file');
        $filepath = public_path('downloads/' . $filename);
        
        if (!file_exists($filepath)) {
            return json(['code' => 404, 'msg' => '文件不存在']);
        }
        
        // 创建流式响应
        return response()->download($filepath, basename($filepath));
    }
    
    /**
     * 服务器发送事件（SSE）
     * @param Request $request
     * @return Response
     */
    public function events(Request $request)
    {
        // 设置SSE响应头
        $response = response();
        $response->header([
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'Access-Control-Allow-Origin' => '*',
        ]);
        
        // 发送初始数据
        $response->write("data: " . json_encode([
            'type' => 'connected',
            'message' => '连接已建立',
            'timestamp' => time()
        ]) . "\n\n");
        
        // 模拟实时数据推送
        for ($i = 1; $i <= 10; $i++) {
            sleep(1);
            
            $data = [
                'type' => 'data',
                'id' => $i,
                'message' => "这是第 {$i} 条消息",
                'timestamp' => time()
            ];
            
            $response->write("data: " . json_encode($data) . "\n\n");
        }
        
        // 发送结束事件
        $response->write("data: " . json_encode([
            'type' => 'end',
            'message' => '数据推送结束',
            'timestamp' => time()
        ]) . "\n\n");
        
        return $response;
    }
    
    /**
     * 分块传输编码
     * @param Request $request
     * @return Response
     */
    public function chunked(Request $request)
    {
        $response = response();
        $response->header('Transfer-Encoding', 'chunked');
        
        // 分块发送数据
        for ($i = 1; $i <= 5; $i++) {
            $chunk = "这是第 {$i} 个数据块\n";
            $response->write($chunk);
            
            // 模拟处理时间
            sleep(1);
        }
        
        return $response;
    }
}
```

### 5. 协程并发处理

```php
<?php
// app/controller/Concurrent.php

namespace app\controller;

use think\Request;
use think\Response;
use Swoole\Coroutine;
use Swoole\Coroutine\Http\Client;

class Concurrent
{
    /**
     * 并发请求处理
     * @param Request $request
     * @return Response
     */
    public function batch(Request $request)
    {
        $urls = $request->param('urls', []);
        
        if (empty($urls)) {
            return json(['code' => 400, 'msg' => 'URLs参数不能为空']);
        }
        
        $results = [];
        $startTime = microtime(true);
        
        // 使用协程并发请求
        Coroutine\batch([
            'api1' => function () use ($urls, &$results) {
                if (isset($urls[0])) {
                    $results['api1'] = $this->httpGet($urls[0]);
                }
            },
            'api2' => function () use ($urls, &$results) {
                if (isset($urls[1])) {
                    $results['api2'] = $this->httpGet($urls[1]);
                }
            },
            'api3' => function () use ($urls, &$results) {
                if (isset($urls[2])) {
                    $results['api3'] = $this->httpGet($urls[2]);
                }
            },
        ]);
        
        $duration = round((microtime(true) - $startTime) * 1000, 2);
        
        return json([
            'code' => 200,
            'msg' => '批量请求完成',
            'data' => [
                'results' => $results,
                'duration' => $duration . 'ms',
                'count' => count($results),
            ]
        ]);
    }
    
    /**
     * HTTP GET请求
     * @param string $url
     * @return array
     */
    private function httpGet(string $url): array
    {
        $urlInfo = parse_url($url);
        $host = $urlInfo['host'];
        $port = $urlInfo['port'] ?? ($urlInfo['scheme'] === 'https' ? 443 : 80);
        $path = $urlInfo['path'] ?? '/';
        $ssl = $urlInfo['scheme'] === 'https';
        
        $client = new Client($host, $port, $ssl);
        $client->set(['timeout' => 10]);
        
        $client->get($path);
        
        return [
            'url' => $url,
            'status_code' => $client->statusCode,
            'headers' => $client->headers,
            'body_length' => strlen($client->body),
            'error' => $client->errCode ? swoole_strerror($client->errCode) : null,
        ];
    }
}
```

## 注意事项

### 1. 内存管理

- 避免内存泄漏，及时释放大对象
- 合理设置max_request参数
- 监控进程内存使用情况

### 2. 协程使用

- 避免在协程中使用阻塞函数
- 合理控制协程数量
- 注意协程间的数据共享

### 3. 文件处理

- 大文件上传使用流式处理
- 设置合理的文件大小限制
- 及时清理临时文件

### 4. 性能优化

- 合理配置Worker进程数
- 使用连接池减少连接开销
- 启用OPcache提升性能

### 5. 安全考虑

- 验证用户输入数据
- 设置合理的请求限制
- 使用HTTPS保护数据传输

## 常见问题

### Q: 如何处理大量并发连接？
A: 调整max_conn参数，使用连接池，优化业务逻辑减少处理时间。

### Q: 静态文件如何处理？
A: 建议使用Nginx处理静态文件，Swoole处理动态请求。

### Q: 如何实现平滑重启？
A: 使用reload命令重新加载Worker进程，不中断服务。

### Q: 内存使用过高怎么办？
A: 检查内存泄漏，调整max_request参数，优化代码逻辑。
