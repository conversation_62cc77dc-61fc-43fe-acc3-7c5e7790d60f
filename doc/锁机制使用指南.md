# 锁机制使用指南

## 功能概述

锁机制是基于Swoole内存表或Redis实现的进程间同步工具，用于解决并发访问共享资源时的竞态条件问题。该功能支持多种锁实现方式，包括内存表锁、Redis分布式锁等，适用于高并发场景下的资源保护和数据一致性保障。

## 适用场景

- 防止重复提交
- 库存扣减保护
- 缓存更新同步
- 定时任务防重复执行
- 分布式系统资源互斥
- 数据库操作串行化
- 文件写入保护

## 配置说明

### 基础配置

在 `config/swoole.php` 文件中配置锁机制：

```php
'lock' => [
    'enable' => true,                       // 是否启用锁功能
    'type' => 'table',                      // 锁类型：table/redis
    
    // Redis锁配置（type=redis时使用）
    'redis' => [
        'host' => '127.0.0.1',              // Redis主机
        'port' => 6379,                     // Redis端口
        'password' => '',                   // Redis密码
        'database' => 0,                    // Redis数据库
        'max_active' => 3,                  // 最大活动连接数
        'max_wait_time' => 5,               // 最大等待时间
    ],
    
    // 内存表锁配置（type=table时使用）
    'table' => [
        'size' => 1024,                     // 内存表大小
    ],
],
```

### 配置项详解

#### 基础配置项

- `enable`: 是否启用锁功能，默认false
- `type`: 锁实现类型，支持 `table`（内存表）和 `redis`（Redis分布式锁）

#### 内存表锁配置

- `size`: 内存表大小，决定了可以同时存在的锁数量

#### Redis锁配置

- `host`: Redis服务器地址
- `port`: Redis服务器端口
- `password`: Redis认证密码
- `database`: Redis数据库编号
- `max_active`: 连接池最大活动连接数
- `max_wait_time`: 获取连接的最大等待时间

## 使用方法

### 1. 基础锁使用

#### 简单加锁解锁

```php
<?php
// app/service/OrderService.php

namespace app\service;

use think\swoole\Lock;
use think\facade\Db;

class OrderService
{
    /**
     * @var Lock 锁管理器
     */
    protected $lock;
    
    public function __construct(Lock $lock)
    {
        $this->lock = $lock;
    }
    
    /**
     * 创建订单（防重复提交）
     * @param int $userId 用户ID
     * @param array $orderData 订单数据
     * @return array
     */
    public function createOrder(int $userId, array $orderData): array
    {
        $lockKey = "create_order_{$userId}";
        
        // 尝试获取锁，防止重复提交
        if (!$this->lock->lock($lockKey, 30)) {
            throw new \Exception('请勿重复提交订单');
        }
        
        try {
            // 检查是否已有未支付订单
            $existingOrder = Db::table('orders')
                ->where('user_id', $userId)
                ->where('status', 'pending')
                ->find();
            
            if ($existingOrder) {
                throw new \Exception('您有未支付的订单，请先完成支付');
            }
            
            // 创建订单
            $orderId = Db::table('orders')->insertGetId([
                'user_id' => $userId,
                'amount' => $orderData['amount'],
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s'),
            ]);
            
            // 创建订单商品
            foreach ($orderData['items'] as $item) {
                Db::table('order_items')->insert([
                    'order_id' => $orderId,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                ]);
            }
            
            return [
                'order_id' => $orderId,
                'status' => 'pending',
                'amount' => $orderData['amount'],
            ];
            
        } finally {
            // 确保释放锁
            $this->lock->unlock($lockKey);
        }
    }
}
```

#### 库存扣减保护

```php
<?php
// app/service/InventoryService.php

namespace app\service;

use think\swoole\Lock;
use think\facade\Db;

class InventoryService
{
    protected $lock;
    
    public function __construct(Lock $lock)
    {
        $this->lock = $lock;
    }
    
    /**
     * 扣减库存
     * @param int $productId 商品ID
     * @param int $quantity 扣减数量
     * @return bool
     */
    public function decreaseStock(int $productId, int $quantity): bool
    {
        $lockKey = "stock_{$productId}";
        
        // 获取库存锁，超时时间10秒
        if (!$this->lock->lock($lockKey, 10)) {
            throw new \Exception('库存操作繁忙，请稍后重试');
        }
        
        try {
            // 查询当前库存
            $product = Db::table('products')
                ->where('id', $productId)
                ->find();
            
            if (!$product) {
                throw new \Exception('商品不存在');
            }
            
            if ($product['stock'] < $quantity) {
                throw new \Exception('库存不足');
            }
            
            // 扣减库存
            $affected = Db::table('products')
                ->where('id', $productId)
                ->where('stock', '>=', $quantity)  // 再次检查库存
                ->dec('stock', $quantity);
            
            if ($affected === 0) {
                throw new \Exception('库存扣减失败，可能库存不足');
            }
            
            // 记录库存变动日志
            Db::table('stock_logs')->insert([
                'product_id' => $productId,
                'type' => 'decrease',
                'quantity' => $quantity,
                'before_stock' => $product['stock'],
                'after_stock' => $product['stock'] - $quantity,
                'created_at' => date('Y-m-d H:i:s'),
            ]);
            
            return true;
            
        } finally {
            $this->lock->unlock($lockKey);
        }
    }
    
    /**
     * 批量扣减库存
     * @param array $items 商品列表 [['product_id' => 1, 'quantity' => 2], ...]
     * @return bool
     */
    public function batchDecreaseStock(array $items): bool
    {
        // 按商品ID排序，避免死锁
        usort($items, function ($a, $b) {
            return $a['product_id'] <=> $b['product_id'];
        });
        
        $locks = [];
        
        try {
            // 按顺序获取所有锁
            foreach ($items as $item) {
                $lockKey = "stock_{$item['product_id']}";
                
                if (!$this->lock->lock($lockKey, 10)) {
                    throw new \Exception("获取商品{$item['product_id']}库存锁失败");
                }
                
                $locks[] = $lockKey;
            }
            
            // 在事务中执行批量扣减
            Db::transaction(function () use ($items) {
                foreach ($items as $item) {
                    $this->doDecreaseStock($item['product_id'], $item['quantity']);
                }
            });
            
            return true;
            
        } finally {
            // 释放所有锁
            foreach ($locks as $lockKey) {
                $this->lock->unlock($lockKey);
            }
        }
    }
    
    /**
     * 执行库存扣减（内部方法）
     * @param int $productId
     * @param int $quantity
     */
    private function doDecreaseStock(int $productId, int $quantity)
    {
        $affected = Db::table('products')
            ->where('id', $productId)
            ->where('stock', '>=', $quantity)
            ->dec('stock', $quantity);
        
        if ($affected === 0) {
            throw new \Exception("商品{$productId}库存不足");
        }
    }
}
```

### 2. 定时任务防重复执行

```php
<?php
// app/command/DataSync.php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\swoole\Lock;

class DataSync extends Command
{
    protected function configure()
    {
        $this->setName('data:sync')
             ->setDescription('数据同步任务');
    }
    
    protected function execute(Input $input, Output $output)
    {
        $lock = app(Lock::class);
        $lockKey = 'task_data_sync';
        
        // 尝试获取锁，防止重复执行
        if (!$lock->lock($lockKey, 3600)) { // 1小时超时
            $output->writeln('数据同步任务正在执行中，跳过本次执行');
            return;
        }
        
        try {
            $output->writeln('开始执行数据同步任务...');
            
            // 执行同步逻辑
            $this->syncUserData();
            $this->syncOrderData();
            $this->syncProductData();
            
            $output->writeln('数据同步任务执行完成');
            
        } catch (\Exception $e) {
            $output->writeln('数据同步任务执行失败: ' . $e->getMessage());
            throw $e;
            
        } finally {
            // 释放锁
            $lock->unlock($lockKey);
        }
    }
    
    /**
     * 同步用户数据
     */
    private function syncUserData()
    {
        // 同步逻辑...
        sleep(10); // 模拟耗时操作
    }
    
    /**
     * 同步订单数据
     */
    private function syncOrderData()
    {
        // 同步逻辑...
        sleep(15);
    }
    
    /**
     * 同步商品数据
     */
    private function syncProductData()
    {
        // 同步逻辑...
        sleep(20);
    }
}
```

### 3. 缓存更新同步

```php
<?php
// app/service/CacheService.php

namespace app\service;

use think\swoole\Lock;
use think\facade\Cache;
use think\facade\Db;

class CacheService
{
    protected $lock;
    
    public function __construct(Lock $lock)
    {
        $this->lock = $lock;
    }
    
    /**
     * 获取用户信息（带缓存）
     * @param int $userId
     * @return array|null
     */
    public function getUserInfo(int $userId): ?array
    {
        $cacheKey = "user_info_{$userId}";
        
        // 先尝试从缓存获取
        $userInfo = Cache::get($cacheKey);
        
        if ($userInfo !== null) {
            return $userInfo;
        }
        
        // 缓存未命中，使用锁防止缓存击穿
        $lockKey = "cache_user_{$userId}";
        
        if (!$this->lock->lock($lockKey, 30)) {
            // 获取锁失败，等待一下再次尝试从缓存获取
            usleep(100000); // 等待100ms
            return Cache::get($cacheKey);
        }
        
        try {
            // 再次检查缓存（双重检查）
            $userInfo = Cache::get($cacheKey);
            if ($userInfo !== null) {
                return $userInfo;
            }
            
            // 从数据库获取数据
            $userInfo = Db::table('users')
                ->where('id', $userId)
                ->find();
            
            if ($userInfo) {
                // 设置缓存，过期时间1小时
                Cache::set($cacheKey, $userInfo, 3600);
            }
            
            return $userInfo;
            
        } finally {
            $this->lock->unlock($lockKey);
        }
    }
    
    /**
     * 更新用户信息并刷新缓存
     * @param int $userId
     * @param array $data
     * @return bool
     */
    public function updateUserInfo(int $userId, array $data): bool
    {
        $lockKey = "cache_user_{$userId}";
        
        if (!$this->lock->lock($lockKey, 30)) {
            throw new \Exception('用户信息更新繁忙，请稍后重试');
        }
        
        try {
            // 更新数据库
            $affected = Db::table('users')
                ->where('id', $userId)
                ->update($data);
            
            if ($affected > 0) {
                // 删除缓存，下次访问时重新加载
                $cacheKey = "user_info_{$userId}";
                Cache::delete($cacheKey);
                
                return true;
            }
            
            return false;
            
        } finally {
            $this->lock->unlock($lockKey);
        }
    }
}
```

### 4. 自定义锁实现

#### 创建自定义Redis锁

```php
<?php
// app/lock/RedisLock.php

namespace app\lock;

use think\swoole\contract\LockInterface;
use Swoole\Coroutine\Redis;

class RedisLock implements LockInterface
{
    /**
     * @var Redis Redis连接
     */
    protected $redis;
    
    /**
     * @var array 配置参数
     */
    protected $config;
    
    public function __construct(array $config)
    {
        $this->config = $config;
    }
    
    /**
     * 准备锁资源
     */
    public function prepare()
    {
        $this->redis = new Redis();
        
        $connected = $this->redis->connect(
            $this->config['host'] ?? '127.0.0.1',
            $this->config['port'] ?? 6379,
            $this->config['timeout'] ?? 1
        );
        
        if (!$connected) {
            throw new \Exception('Redis连接失败');
        }
        
        if (!empty($this->config['password'])) {
            $this->redis->auth($this->config['password']);
        }
        
        if (isset($this->config['database'])) {
            $this->redis->select($this->config['database']);
        }
    }
    
    /**
     * 加锁
     * @param string $name 锁名称
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public function lock(string $name, int $expire = 60): bool
    {
        $lockKey = "lock:{$name}";
        $lockValue = uniqid() . ':' . getmypid();
        
        // 使用SET命令的NX和EX选项实现原子性加锁
        $result = $this->redis->set($lockKey, $lockValue, ['NX', 'EX' => $expire]);
        
        return $result === true;
    }
    
    /**
     * 解锁
     * @param string $name 锁名称
     */
    public function unlock(string $name)
    {
        $lockKey = "lock:{$name}";
        
        // 使用Lua脚本确保原子性解锁
        $script = '
            if redis.call("get", KEYS[1]) == ARGV[1] then
                return redis.call("del", KEYS[1])
            else
                return 0
            end
        ';
        
        $lockValue = uniqid() . ':' . getmypid();
        $this->redis->eval($script, [$lockKey, $lockValue], 1);
    }
    
    /**
     * 尝试加锁（非阻塞）
     * @param string $name 锁名称
     * @param int $expire 过期时间
     * @return bool
     */
    public function tryLock(string $name, int $expire = 60): bool
    {
        return $this->lock($name, $expire);
    }
    
    /**
     * 阻塞式加锁
     * @param string $name 锁名称
     * @param int $expire 过期时间
     * @param int $timeout 等待超时时间
     * @return bool
     */
    public function blockLock(string $name, int $expire = 60, int $timeout = 10): bool
    {
        $startTime = time();
        
        while (time() - $startTime < $timeout) {
            if ($this->lock($name, $expire)) {
                return true;
            }
            
            // 等待100ms后重试
            usleep(100000);
        }
        
        return false;
    }
}
```

## 注意事项

### 1. 死锁预防

- 按固定顺序获取多个锁
- 设置合理的锁超时时间
- 避免在持有锁时进行阻塞操作

### 2. 锁的粒度

- 选择合适的锁粒度，避免过粗或过细
- 考虑业务场景的并发特点
- 平衡性能和数据一致性

### 3. 异常处理

- 使用try-finally确保锁被释放
- 处理锁获取失败的情况
- 记录锁相关的异常信息

### 4. 性能考虑

- 避免长时间持有锁
- 合理设置锁的超时时间
- 监控锁的竞争情况

### 5. 分布式锁注意事项

- 考虑网络分区的影响
- 处理Redis连接失败的情况
- 实现锁的自动续期机制

## 常见问题

### Q: 如何选择内存表锁还是Redis锁？
A: 单机部署使用内存表锁性能更好，分布式部署必须使用Redis锁。

### Q: 锁超时时间如何设置？
A: 根据业务逻辑的执行时间设置，通常比预期执行时间长50-100%。

### Q: 如何避免死锁？
A: 按固定顺序获取锁，设置超时时间，避免嵌套锁。

### Q: 锁竞争激烈怎么办？
A: 优化业务逻辑减少锁持有时间，或考虑使用队列等其他方案。
