# 热更新机制使用指南

## 功能概述

热更新机制是基于文件监控实现的自动代码重载功能，当检测到项目文件发生变化时，自动重启相关的工作进程，实现代码的热更新。该功能支持多种文件监控驱动，包括Find、Scan、Fswatch等，适用于开发环境的快速调试和测试。

## 适用场景

- 开发环境的代码调试
- 配置文件的动态更新
- 模板文件的实时预览
- API接口的快速测试
- 微服务的开发调试

## 配置说明

### 基础配置

在 `config/swoole.php` 文件中配置热更新：

```php
'hot_update' => [
    // 是否启用热更新
    'enable' => env('swoole.hot_update', false),
    
    // 驱动类型，linux建议使用Find，否则使用Scan
    'type' => 'Find',
    
    // 监控文件类型
    'name' => ['*.php', '*.env', '*.yaml', '*.yml'],
    
    // 监控目录
    'include' => [
        app()->getRootPath() . 'view' . DIRECTORY_SEPARATOR,
        app()->getBasePath(),
        app()->getConfigPath(),
    ],
    
    // 排除目录
    'exclude' => [
        app()->getRuntimePath(),
        app()->getRootPath() . 'vendor',
        app()->getRootPath() . 'node_modules',
    ],
    
    // Find驱动特定配置
    'find' => [
        'interval' => 2000,     // 检查间隔（毫秒）
        'minutes' => 0.1,       // 检查最近修改的文件（分钟）
    ],
    
    // Scan驱动特定配置
    'scan' => [
        'interval' => 2000,     // 扫描间隔（毫秒）
    ],
    
    // Fswatch驱动特定配置（需要安装fswatch）
    'fswatch' => [
        'events' => ['Created', 'Updated', 'Removed', 'Renamed'],
    ],
],
```

### 环境变量配置

在 `.env` 文件中配置热更新开关：

```bash
# 开发环境启用热更新
SWOOLE_HOT_UPDATE=true

# 生产环境关闭热更新
# SWOOLE_HOT_UPDATE=false
```

### 配置项详解

#### 基础配置项

- `enable`: 是否启用热更新功能，建议只在开发环境启用
- `type`: 监控驱动类型，支持 `Find`、`Scan`、`Fswatch`
- `name`: 监控的文件类型，支持通配符匹配
- `include`: 需要监控的目录列表
- `exclude`: 需要排除的目录列表

#### 驱动选择建议

- **Find**: 适用于Linux/Unix系统，性能较好，推荐使用
- **Scan**: 适用于所有系统，通过扫描文件修改时间检测变化
- **Fswatch**: 需要安装fswatch工具，实时性最好但依赖外部工具

## 使用方法

### 1. 启用热更新

#### 开发环境配置

```php
// config/swoole.php
'hot_update' => [
    'enable' => true,
    'type' => 'Find',
    'name' => ['*.php', '*.env'],
    'include' => [
        app()->getBasePath(),
        app()->getConfigPath(),
    ],
    'exclude' => [
        app()->getRuntimePath(),
    ],
],
```

#### 启动服务

```bash
# 启动Swoole服务（自动启用热更新）
php think swoole

# 或者明确指定启用热更新
SWOOLE_HOT_UPDATE=true php think swoole
```

### 2. 自定义监控驱动

#### 创建自定义驱动

```php
<?php
// app/watcher/CustomDriver.php

namespace app\watcher;

use think\swoole\watcher\Driver;
use Swoole\Timer;

class CustomDriver extends Driver
{
    /**
     * @var int 定时器ID
     */
    protected $timer;
    
    /**
     * @var array 配置参数
     */
    protected $config;
    
    /**
     * 构造函数
     * @param array $config 配置参数
     */
    public function __construct(array $config)
    {
        $this->config = $config;
    }
    
    /**
     * 开始监控
     * @param callable $callback 回调函数
     */
    public function watch(callable $callback)
    {
        $interval = $this->config['interval'] ?? 2000;
        $lastCheck = time();
        
        $this->timer = Timer::tick($interval, function () use ($callback, &$lastCheck) {
            $changedFiles = $this->getChangedFiles($lastCheck);
            
            if (!empty($changedFiles)) {
                $callback($changedFiles);
                $lastCheck = time();
            }
        });
    }
    
    /**
     * 停止监控
     */
    public function stop()
    {
        if ($this->timer) {
            Timer::clear($this->timer);
            $this->timer = null;
        }
    }
    
    /**
     * 获取变化的文件
     * @param int $since 检查时间点
     * @return array 变化的文件列表
     */
    protected function getChangedFiles(int $since): array
    {
        $changedFiles = [];
        $directories = $this->config['directory'] ?? [];
        $patterns = $this->config['name'] ?? ['*.php'];
        
        foreach ($directories as $directory) {
            if (!is_dir($directory)) {
                continue;
            }
            
            $files = $this->scanDirectory($directory, $patterns);
            
            foreach ($files as $file) {
                if (filemtime($file) > $since) {
                    $changedFiles[] = $file;
                }
            }
        }
        
        return $changedFiles;
    }
    
    /**
     * 扫描目录
     * @param string $directory 目录路径
     * @param array $patterns 文件模式
     * @return array 文件列表
     */
    protected function scanDirectory(string $directory, array $patterns): array
    {
        $files = [];
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $filename = $file->getFilename();
                
                foreach ($patterns as $pattern) {
                    if (fnmatch($pattern, $filename)) {
                        $files[] = $file->getPathname();
                        break;
                    }
                }
            }
        }
        
        return $files;
    }
}
```

#### 注册自定义驱动

```php
// config/swoole.php
'hot_update' => [
    'enable' => true,
    'type' => 'Custom',  // 使用自定义驱动
    'custom' => [        // 自定义驱动配置
        'class' => \app\watcher\CustomDriver::class,
        'interval' => 1000,
    ],
    // 其他配置...
],
```

### 3. 监控特定文件类型

#### 监控配置文件

```php
// config/swoole.php
'hot_update' => [
    'enable' => true,
    'type' => 'Find',
    'name' => ['*.php', '*.env', '*.yaml', '*.yml', '*.json'],
    'include' => [
        app()->getConfigPath(),
        app()->getRootPath() . 'config',
    ],
],
```

#### 监控模板文件

```php
// config/swoole.php
'hot_update' => [
    'enable' => true,
    'type' => 'Find',
    'name' => ['*.html', '*.tpl', '*.php'],
    'include' => [
        app()->getRootPath() . 'view',
        app()->getRootPath() . 'template',
    ],
],
```

### 4. 高级配置示例

#### 完整的开发环境配置

```php
// config/swoole.php
'hot_update' => [
    'enable' => env('APP_DEBUG', false),
    'type' => PHP_OS_FAMILY === 'Linux' ? 'Find' : 'Scan',
    
    // 监控文件类型
    'name' => [
        '*.php',        // PHP文件
        '*.env',        // 环境配置
        '*.yaml',       // YAML配置
        '*.yml',        // YAML配置
        '*.json',       // JSON配置
        '*.html',       // HTML模板
        '*.js',         // JavaScript文件
        '*.css',        // CSS文件
    ],
    
    // 监控目录
    'include' => [
        app()->getBasePath(),           // 应用目录
        app()->getConfigPath(),         // 配置目录
        app()->getRootPath() . 'view',  // 视图目录
        app()->getRootPath() . 'route', // 路由目录
        app()->getRootPath() . 'public/static', // 静态资源
    ],
    
    // 排除目录
    'exclude' => [
        app()->getRuntimePath(),                    // 运行时目录
        app()->getRootPath() . 'vendor',           // 第三方包
        app()->getRootPath() . 'node_modules',     // Node模块
        app()->getRootPath() . '.git',             // Git目录
        app()->getRootPath() . 'storage/logs',     // 日志目录
    ],
    
    // Find驱动配置
    'find' => [
        'interval' => 1000,     // 1秒检查一次
        'minutes' => 0.05,      // 检查最近3秒内修改的文件
    ],
],
```

#### 生产环境安全配置

```php
// config/swoole.php
'hot_update' => [
    // 生产环境强制关闭
    'enable' => false,
    
    // 即使意外启用，也限制监控范围
    'name' => ['*.php'],
    'include' => [
        app()->getBasePath() . 'config',  // 只监控配置文件
    ],
    'exclude' => [
        app()->getBasePath(),  // 排除应用代码
    ],
],
```

## 示例代码

### 热更新事件监听

```php
<?php
// app/listener/HotUpdateListener.php

namespace app\listener;

use think\facade\Log;

class HotUpdateListener
{
    /**
     * 处理热更新事件
     * @param array $files 变化的文件列表
     */
    public function handle(array $files)
    {
        foreach ($files as $file) {
            Log::info("检测到文件变化: {$file}");
            
            // 根据文件类型执行不同操作
            $extension = pathinfo($file, PATHINFO_EXTENSION);
            
            switch ($extension) {
                case 'php':
                    $this->handlePhpFileChange($file);
                    break;
                    
                case 'env':
                    $this->handleEnvFileChange($file);
                    break;
                    
                case 'yaml':
                case 'yml':
                    $this->handleConfigFileChange($file);
                    break;
                    
                default:
                    $this->handleOtherFileChange($file);
            }
        }
    }
    
    /**
     * 处理PHP文件变化
     * @param string $file 文件路径
     */
    protected function handlePhpFileChange(string $file)
    {
        Log::info("PHP文件已更新，准备重载: {$file}");
        
        // 清除OPcache
        if (function_exists('opcache_invalidate')) {
            opcache_invalidate($file, true);
        }
        
        // 可以在这里添加其他处理逻辑
        // 比如清除特定缓存、重新加载配置等
    }
    
    /**
     * 处理环境配置文件变化
     * @param string $file 文件路径
     */
    protected function handleEnvFileChange(string $file)
    {
        Log::info("环境配置文件已更新: {$file}");
        
        // 重新加载环境变量
        if (class_exists('\Dotenv\Dotenv')) {
            $dotenv = \Dotenv\Dotenv::createImmutable(dirname($file));
            $dotenv->load();
        }
    }
    
    /**
     * 处理配置文件变化
     * @param string $file 文件路径
     */
    protected function handleConfigFileChange(string $file)
    {
        Log::info("配置文件已更新: {$file}");
        
        // 清除配置缓存
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }
    }
    
    /**
     * 处理其他文件变化
     * @param string $file 文件路径
     */
    protected function handleOtherFileChange(string $file)
    {
        Log::info("其他文件已更新: {$file}");
    }
}
```

### 自定义热更新处理

```php
<?php
// app/service/HotUpdateService.php

namespace app\service;

use think\facade\Cache;
use think\facade\Log;

class HotUpdateService
{
    /**
     * 处理热更新
     * @param array $files 变化的文件列表
     */
    public function handle(array $files)
    {
        $this->logFileChanges($files);
        $this->clearCache($files);
        $this->notifyDevelopers($files);
    }
    
    /**
     * 记录文件变化日志
     * @param array $files 文件列表
     */
    protected function logFileChanges(array $files)
    {
        $changeLog = [
            'timestamp' => date('Y-m-d H:i:s'),
            'files' => $files,
            'count' => count($files),
        ];
        
        Log::channel('hot_update')->info('文件变化检测', $changeLog);
    }
    
    /**
     * 清除相关缓存
     * @param array $files 文件列表
     */
    protected function clearCache(array $files)
    {
        foreach ($files as $file) {
            // 根据文件路径判断需要清除的缓存
            if (strpos($file, 'config') !== false) {
                Cache::tag('config')->clear();
            }
            
            if (strpos($file, 'route') !== false) {
                Cache::tag('route')->clear();
            }
            
            if (strpos($file, 'view') !== false) {
                Cache::tag('template')->clear();
            }
        }
    }
    
    /**
     * 通知开发者
     * @param array $files 文件列表
     */
    protected function notifyDevelopers(array $files)
    {
        // 可以通过WebSocket、邮件等方式通知开发者
        $message = sprintf(
            '检测到 %d 个文件发生变化，服务已自动重载',
            count($files)
        );
        
        // 示例：通过WebSocket推送通知
        try {
            $websocket = app('websocket');
            $websocket->to('developers')->emit('hot_update', [
                'message' => $message,
                'files' => $files,
                'timestamp' => time(),
            ]);
        } catch (\Exception $e) {
            // 忽略WebSocket推送失败
        }
    }
}
```

## 注意事项

### 1. 性能影响

- 热更新会消耗额外的CPU和内存资源
- 建议只在开发环境启用
- 合理设置检查间隔，避免过于频繁

### 2. 文件监控范围

- 避免监控大量不必要的文件
- 排除日志、缓存等频繁变化的目录
- 合理使用文件类型过滤

### 3. 安全考虑

- 生产环境必须关闭热更新功能
- 避免监控敏感文件和目录
- 限制热更新的触发权限

### 4. 兼容性

- 不同操作系统的文件监控机制不同
- 某些网络文件系统可能不支持实时监控
- 容器环境下需要特殊配置

### 5. 调试技巧

- 查看热更新日志定位问题
- 使用合适的监控驱动
- 测试文件变化检测是否正常

## 常见问题

### Q: 热更新不生效怎么办？
A: 检查配置是否正确，确认监控目录和文件类型设置，查看日志输出。

### Q: 热更新频率太高怎么办？
A: 调整检查间隔，排除不必要的监控目录，使用更精确的文件过滤。

### Q: 在Docker中如何使用热更新？
A: 确保文件挂载正确，可能需要使用polling模式而不是inotify。

### Q: 热更新会影响性能吗？
A: 会有一定影响，建议只在开发环境使用，生产环境务必关闭。
