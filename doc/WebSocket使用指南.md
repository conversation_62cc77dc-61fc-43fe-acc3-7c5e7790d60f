# WebSocket使用指南

## 功能概述

WebSocket功能基于Swoole实现了高性能的实时双向通信，支持房间管理、消息推送、事件处理等特性。该功能提供了完整的WebSocket解决方案，包括连接管理、房间系统、消息广播、心跳检测等功能。

## 适用场景

- 实时聊天应用
- 在线游戏
- 实时数据推送
- 协作编辑工具
- 实时监控系统
- 直播弹幕系统

## 配置说明

### 基础配置

在 `config/swoole.php` 文件中配置WebSocket服务：

```php
'websocket' => [
    'enable' => true,                       // 是否启用WebSocket
    'route' => true,                        // 是否启用路由
    'handler' => \think\swoole\websocket\Handler::class, // 处理器类
    'ping_interval' => 25000,               // 心跳检测间隔（毫秒）
    'ping_timeout' => 60000,                // 心跳超时时间（毫秒）
    
    // 房间配置
    'room' => [
        'type' => 'table',                  // 房间存储类型：table/redis
        
        // 内存表配置（type=table时使用）
        'table' => [
            'room_rows' => 8192,            // 房间表行数
            'room_size' => 2048,            // 房间数据大小
            'client_rows' => 4096,          // 客户端表行数
            'client_size' => 2048,          // 客户端数据大小
        ],
        
        // Redis配置（type=redis时使用）
        'redis' => [
            'host' => '127.0.0.1',          // Redis主机
            'port' => 6379,                 // Redis端口
            'max_active' => 3,              // 最大活动连接数
            'max_wait_time' => 5,           // 最大等待时间
        ],
    ],
    
    // 事件监听配置
    'listen' => [
        'connect' => \app\websocket\Connect::class,
        'message' => \app\websocket\Message::class,
        'close' => \app\websocket\Close::class,
    ],
    
    // 订阅配置
    'subscribe' => [
        'user_login' => \app\websocket\UserLogin::class,
        'order_update' => \app\websocket\OrderUpdate::class,
    ],
],
```

### 配置项详解

#### 基础配置项

- `enable`: 是否启用WebSocket功能
- `route`: 是否启用WebSocket路由功能
- `handler`: WebSocket处理器类
- `ping_interval`: 心跳检测发送间隔
- `ping_timeout`: 心跳检测超时时间

#### 房间配置项

- `type`: 房间数据存储方式，支持 `table`（内存表）和 `redis`
- `table`: 内存表配置，适用于单机部署
- `redis`: Redis配置，适用于分布式部署

## 使用方法

### 1. 创建WebSocket事件处理器

#### 连接事件处理器

```php
<?php
// app/websocket/Connect.php

namespace app\websocket;

use think\Request;
use think\swoole\Websocket;

class Connect
{
    /**
     * 处理WebSocket连接事件
     * @param Request $request HTTP升级请求对象
     * @param Websocket $websocket WebSocket实例
     */
    public function handle(Request $request, Websocket $websocket)
    {
        // 获取连接参数
        $token = $request->param('token');
        $userId = $this->getUserIdFromToken($token);
        
        if (!$userId) {
            // 验证失败，关闭连接
            $websocket->close();
            return;
        }
        
        // 将用户加入到用户房间
        $websocket->join("user_{$userId}");
        
        // 加入到在线用户房间
        $websocket->join('online_users');
        
        // 发送欢迎消息
        $websocket->emit('welcome', [
            'message' => '欢迎连接到WebSocket服务',
            'user_id' => $userId,
            'timestamp' => time()
        ]);
        
        // 通知其他用户有新用户上线
        $websocket->to('online_users')->emit('user_online', [
            'user_id' => $userId,
            'timestamp' => time()
        ]);
        
        // 记录连接日志
        trace("用户 {$userId} 连接到WebSocket服务");
    }
    
    /**
     * 从token获取用户ID
     * @param string $token
     * @return int|null
     */
    private function getUserIdFromToken(string $token): ?int
    {
        // 这里实现你的token验证逻辑
        // 例如：JWT解析、数据库查询等
        if (empty($token)) {
            return null;
        }
        
        // 示例：简单的token验证
        $userData = cache("websocket_token_{$token}");
        return $userData['user_id'] ?? null;
    }
}
```

#### 消息事件处理器

```php
<?php
// app/websocket/Message.php

namespace app\websocket;

use Swoole\WebSocket\Frame;
use think\swoole\Websocket;

class Message
{
    /**
     * 处理WebSocket消息事件
     * @param Frame $frame 消息帧对象
     * @param Websocket $websocket WebSocket实例
     */
    public function handle(Frame $frame, Websocket $websocket)
    {
        // 解析消息数据
        $data = json_decode($frame->data, true);
        
        if (!$data || !isset($data['type'])) {
            $websocket->emit('error', ['message' => '消息格式错误']);
            return;
        }
        
        // 根据消息类型处理
        switch ($data['type']) {
            case 'chat':
                $this->handleChatMessage($data, $websocket);
                break;
                
            case 'join_room':
                $this->handleJoinRoom($data, $websocket);
                break;
                
            case 'leave_room':
                $this->handleLeaveRoom($data, $websocket);
                break;
                
            case 'private_message':
                $this->handlePrivateMessage($data, $websocket);
                break;
                
            default:
                $websocket->emit('error', ['message' => '未知的消息类型']);
        }
    }
    
    /**
     * 处理聊天消息
     * @param array $data 消息数据
     * @param Websocket $websocket WebSocket实例
     */
    private function handleChatMessage(array $data, Websocket $websocket)
    {
        $roomId = $data['room_id'] ?? 'public';
        $message = $data['message'] ?? '';
        $userId = $this->getCurrentUserId($websocket);
        
        if (empty($message)) {
            $websocket->emit('error', ['message' => '消息内容不能为空']);
            return;
        }
        
        // 构造消息数据
        $messageData = [
            'type' => 'chat',
            'room_id' => $roomId,
            'user_id' => $userId,
            'message' => $message,
            'timestamp' => time()
        ];
        
        // 向房间内所有用户广播消息
        $websocket->to("room_{$roomId}")->emit('chat_message', $messageData);
        
        // 记录聊天日志
        trace("用户 {$userId} 在房间 {$roomId} 发送消息: {$message}");
    }
    
    /**
     * 处理加入房间
     * @param array $data 消息数据
     * @param Websocket $websocket WebSocket实例
     */
    private function handleJoinRoom(array $data, Websocket $websocket)
    {
        $roomId = $data['room_id'] ?? '';
        $userId = $this->getCurrentUserId($websocket);
        
        if (empty($roomId)) {
            $websocket->emit('error', ['message' => '房间ID不能为空']);
            return;
        }
        
        // 加入房间
        $websocket->join("room_{$roomId}");
        
        // 确认加入成功
        $websocket->emit('room_joined', [
            'room_id' => $roomId,
            'user_id' => $userId,
            'timestamp' => time()
        ]);
        
        // 通知房间内其他用户
        $websocket->to("room_{$roomId}")->emit('user_joined', [
            'room_id' => $roomId,
            'user_id' => $userId,
            'timestamp' => time()
        ]);
    }
    
    /**
     * 处理离开房间
     * @param array $data 消息数据
     * @param Websocket $websocket WebSocket实例
     */
    private function handleLeaveRoom(array $data, Websocket $websocket)
    {
        $roomId = $data['room_id'] ?? '';
        $userId = $this->getCurrentUserId($websocket);
        
        if (empty($roomId)) {
            $websocket->emit('error', ['message' => '房间ID不能为空']);
            return;
        }
        
        // 离开房间
        $websocket->leave("room_{$roomId}");
        
        // 确认离开成功
        $websocket->emit('room_left', [
            'room_id' => $roomId,
            'user_id' => $userId,
            'timestamp' => time()
        ]);
        
        // 通知房间内其他用户
        $websocket->to("room_{$roomId}")->emit('user_left', [
            'room_id' => $roomId,
            'user_id' => $userId,
            'timestamp' => time()
        ]);
    }
    
    /**
     * 处理私聊消息
     * @param array $data 消息数据
     * @param Websocket $websocket WebSocket实例
     */
    private function handlePrivateMessage(array $data, Websocket $websocket)
    {
        $targetUserId = $data['target_user_id'] ?? 0;
        $message = $data['message'] ?? '';
        $senderId = $this->getCurrentUserId($websocket);
        
        if (empty($targetUserId) || empty($message)) {
            $websocket->emit('error', ['message' => '目标用户ID和消息内容不能为空']);
            return;
        }
        
        // 构造私聊消息数据
        $messageData = [
            'type' => 'private_message',
            'sender_id' => $senderId,
            'target_user_id' => $targetUserId,
            'message' => $message,
            'timestamp' => time()
        ];
        
        // 发送给目标用户
        $websocket->to("user_{$targetUserId}")->emit('private_message', $messageData);
        
        // 给发送者确认
        $websocket->emit('message_sent', $messageData);
    }
    
    /**
     * 获取当前用户ID
     * @param Websocket $websocket
     * @return int
     */
    private function getCurrentUserId(Websocket $websocket): int
    {
        // 这里实现获取当前连接用户ID的逻辑
        // 可以从连接上下文、session等获取
        return 1; // 示例返回
    }
}
```

#### 断开连接事件处理器

```php
<?php
// app/websocket/Close.php

namespace app\websocket;

use think\swoole\Websocket;

class Close
{
    /**
     * 处理WebSocket断开连接事件
     * @param Websocket $websocket WebSocket实例
     */
    public function handle(Websocket $websocket)
    {
        $userId = $this->getCurrentUserId($websocket);
        
        if ($userId) {
            // 通知其他用户该用户已下线
            $websocket->to('online_users')->emit('user_offline', [
                'user_id' => $userId,
                'timestamp' => time()
            ]);
            
            // 记录断开连接日志
            trace("用户 {$userId} 断开WebSocket连接");
        }
    }
    
    /**
     * 获取当前用户ID
     * @param Websocket $websocket
     * @return int|null
     */
    private function getCurrentUserId(Websocket $websocket): ?int
    {
        // 实现获取用户ID的逻辑
        return null;
    }
}
```

### 2. 在控制器中推送消息

```php
<?php
// app/controller/Notification.php

namespace app\controller;

use think\Request;
use think\Response;
use think\swoole\Websocket;

class Notification
{
    /**
     * 推送系统通知
     * @param Request $request
     * @return Response
     */
    public function pushSystemNotice(Request $request)
    {
        $message = $request->param('message');
        $targetUsers = $request->param('target_users', []); // 目标用户ID数组
        
        if (empty($message)) {
            return json(['code' => 400, 'msg' => '消息内容不能为空']);
        }
        
        try {
            $websocket = app(Websocket::class);
            
            // 构造通知数据
            $noticeData = [
                'type' => 'system_notice',
                'message' => $message,
                'timestamp' => time()
            ];
            
            if (empty($targetUsers)) {
                // 推送给所有在线用户
                $websocket->to('online_users')->emit('system_notice', $noticeData);
            } else {
                // 推送给指定用户
                foreach ($targetUsers as $userId) {
                    $websocket->to("user_{$userId}")->emit('system_notice', $noticeData);
                }
            }
            
            return json(['code' => 200, 'msg' => '推送成功']);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '推送失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 推送订单状态更新
     * @param Request $request
     * @return Response
     */
    public function pushOrderUpdate(Request $request)
    {
        $orderId = $request->param('order_id');
        $userId = $request->param('user_id');
        $status = $request->param('status');
        
        if (!$orderId || !$userId || !$status) {
            return json(['code' => 400, 'msg' => '参数不完整']);
        }
        
        try {
            $websocket = app(Websocket::class);
            
            // 推送订单更新消息
            $websocket->to("user_{$userId}")->emit('order_update', [
                'order_id' => $orderId,
                'status' => $status,
                'message' => $this->getOrderStatusMessage($status),
                'timestamp' => time()
            ]);
            
            return json(['code' => 200, 'msg' => '推送成功']);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '推送失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取订单状态消息
     * @param string $status
     * @return string
     */
    private function getOrderStatusMessage(string $status): string
    {
        $messages = [
            'paid' => '订单已支付',
            'shipped' => '订单已发货',
            'delivered' => '订单已送达',
            'cancelled' => '订单已取消',
        ];
        
        return $messages[$status] ?? '订单状态已更新';
    }
}
```

### 3. 前端JavaScript客户端示例

```html
<!DOCTYPE html>
<html>
<head>
    <title>WebSocket客户端示例</title>
</head>
<body>
    <div id="messages"></div>
    <input type="text" id="messageInput" placeholder="输入消息...">
    <button onclick="sendMessage()">发送</button>
    <button onclick="joinRoom()">加入房间</button>
    <button onclick="leaveRoom()">离开房间</button>

    <script>
        // 建立WebSocket连接
        const ws = new WebSocket('ws://localhost:8080?token=your_token_here');
        
        // 连接打开事件
        ws.onopen = function(event) {
            console.log('WebSocket连接已建立');
            addMessage('系统', '连接已建立');
        };
        
        // 接收消息事件
        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            console.log('收到消息:', data);
            
            switch(data.type) {
                case 'welcome':
                    addMessage('系统', data.data.message);
                    break;
                    
                case 'chat_message':
                    addMessage(`用户${data.data.user_id}`, data.data.message);
                    break;
                    
                case 'system_notice':
                    addMessage('系统通知', data.data.message);
                    break;
                    
                case 'order_update':
                    addMessage('订单更新', `订单${data.data.order_id}: ${data.data.message}`);
                    break;
                    
                case 'user_online':
                    addMessage('系统', `用户${data.data.user_id}上线了`);
                    break;
                    
                case 'user_offline':
                    addMessage('系统', `用户${data.data.user_id}下线了`);
                    break;
                    
                case 'error':
                    addMessage('错误', data.data.message);
                    break;
            }
        };
        
        // 连接关闭事件
        ws.onclose = function(event) {
            console.log('WebSocket连接已关闭');
            addMessage('系统', '连接已关闭');
        };
        
        // 连接错误事件
        ws.onerror = function(error) {
            console.error('WebSocket错误:', error);
            addMessage('系统', '连接发生错误');
        };
        
        // 发送聊天消息
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message) {
                ws.send(JSON.stringify({
                    type: 'chat',
                    room_id: 'public',
                    message: message
                }));
                input.value = '';
            }
        }
        
        // 加入房间
        function joinRoom() {
            const roomId = prompt('请输入房间ID:');
            if (roomId) {
                ws.send(JSON.stringify({
                    type: 'join_room',
                    room_id: roomId
                }));
            }
        }
        
        // 离开房间
        function leaveRoom() {
            const roomId = prompt('请输入要离开的房间ID:');
            if (roomId) {
                ws.send(JSON.stringify({
                    type: 'leave_room',
                    room_id: roomId
                }));
            }
        }
        
        // 添加消息到页面
        function addMessage(sender, message) {
            const messagesDiv = document.getElementById('messages');
            const messageElement = document.createElement('div');
            messageElement.innerHTML = `<strong>${sender}:</strong> ${message}`;
            messagesDiv.appendChild(messageElement);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
```

## 注意事项

### 1. 连接管理

- WebSocket连接会占用服务器资源，需要合理控制连接数量
- 实现心跳检测机制，及时清理无效连接
- 在连接断开时清理相关资源和房间信息

### 2. 房间管理

- 房间名称要有规范，避免冲突
- 定期清理空房间，释放内存
- 考虑房间人数限制，防止单个房间过大

### 3. 消息处理

- 对接收到的消息进行验证和过滤
- 实现消息限流，防止恶意刷屏
- 敏感消息要进行内容审核

### 4. 性能优化

- 使用Redis存储房间信息支持分布式部署
- 对频繁推送的消息进行批量处理
- 合理设置心跳间隔，平衡性能和实时性

### 5. 安全考虑

- 实现连接认证机制
- 对用户权限进行验证
- 防止XSS和注入攻击

## 常见问题

### Q: WebSocket连接经常断开怎么办？
A: 检查心跳配置，确保客户端正确响应ping消息，调整超时时间。

### Q: 如何实现消息持久化？
A: 在消息处理器中将重要消息存储到数据库或消息队列。

### Q: 支持Socket.IO协议吗？
A: 支持，可以使用 `think\swoole\websocket\socketio\Handler` 处理器。

### Q: 如何实现分布式WebSocket？
A: 使用Redis作为房间存储，配合消息队列实现跨服务器通信。
