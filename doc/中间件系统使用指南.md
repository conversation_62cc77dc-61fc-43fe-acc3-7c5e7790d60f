# 中间件系统使用指南

## 功能概述

中间件系统是基于管道模式实现的请求处理机制，允许在请求处理的不同阶段插入自定义逻辑。该系统支持RPC服务端、RPC客户端、WebSocket等多种场景的中间件，提供了灵活的请求拦截、处理和响应修改能力。

## 适用场景

- 身份验证和权限控制
- 请求日志记录
- 性能监控和链路跟踪
- 请求参数验证
- 响应数据格式化
- 缓存处理
- 限流和防护

## 配置说明

### RPC服务端中间件配置

在 `config/swoole.php` 文件中配置RPC服务端中间件：

```php
'rpc' => [
    'server' => [
        'enable' => true,
        'middleware' => [
            \think\swoole\middleware\TraceRpcServer::class,
            \app\middleware\RpcAuthMiddleware::class,
            \app\middleware\RpcLogMiddleware::class,
        ],
    ],
],
```

### RPC客户端中间件配置

```php
'rpc' => [
    'client' => [
        'user' => [
            'host' => '127.0.0.1',
            'port' => 9090,
            'middleware' => [
                \think\swoole\middleware\TraceRpcClient::class,
                \app\middleware\RpcClientLogMiddleware::class,
            ],
        ],
    ],
],
```

### WebSocket中间件配置

```php
'websocket' => [
    'enable' => true,
    'middleware' => [
        \app\middleware\WebSocketAuthMiddleware::class,
        \app\middleware\WebSocketLogMiddleware::class,
    ],
],
```

## 使用方法

### 1. 创建RPC服务端中间件

#### 身份验证中间件

```php
<?php
// app/middleware/RpcAuthMiddleware.php

namespace app\middleware;

use think\swoole\rpc\Protocol;
use Closure;

class RpcAuthMiddleware
{
    /**
     * 处理RPC请求
     * @param Protocol $protocol RPC协议对象
     * @param Closure $next 下一个中间件
     * @return mixed
     */
    public function handle(Protocol $protocol, Closure $next)
    {
        // 获取请求上下文
        $context = $protocol->getContext();
        $interface = $protocol->getInterface();
        $method = $protocol->getMethod();
        
        // 检查是否需要认证
        if ($this->needsAuth($interface, $method)) {
            // 验证token
            $token = $context['token'] ?? '';
            
            if (!$this->validateToken($token)) {
                throw new \Exception('认证失败：无效的访问令牌', 401);
            }
            
            // 获取用户信息并添加到上下文
            $userInfo = $this->getUserFromToken($token);
            $context['user'] = $userInfo;
            $protocol->setContext($context);
        }
        
        // 继续执行下一个中间件
        return $next($protocol);
    }
    
    /**
     * 检查接口方法是否需要认证
     * @param string $interface 接口名
     * @param string $method 方法名
     * @return bool
     */
    protected function needsAuth(string $interface, string $method): bool
    {
        // 定义不需要认证的接口和方法
        $publicMethods = [
            'UserService' => ['login', 'register'],
            'SystemService' => ['getVersion', 'getStatus'],
        ];
        
        $interfaceName = basename(str_replace('\\', '/', $interface));
        
        return !isset($publicMethods[$interfaceName]) || 
               !in_array($method, $publicMethods[$interfaceName]);
    }
    
    /**
     * 验证token
     * @param string $token
     * @return bool
     */
    protected function validateToken(string $token): bool
    {
        if (empty($token)) {
            return false;
        }
        
        // 这里实现token验证逻辑
        // 例如：JWT验证、数据库查询等
        try {
            // 示例：简单的token验证
            $tokenData = cache("rpc_token_{$token}");
            return !empty($tokenData) && $tokenData['expires'] > time();
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 从token获取用户信息
     * @param string $token
     * @return array
     */
    protected function getUserFromToken(string $token): array
    {
        $tokenData = cache("rpc_token_{$token}");
        
        return [
            'id' => $tokenData['user_id'],
            'username' => $tokenData['username'],
            'roles' => $tokenData['roles'] ?? [],
        ];
    }
}
```

#### 日志记录中间件

```php
<?php
// app/middleware/RpcLogMiddleware.php

namespace app\middleware;

use think\swoole\rpc\Protocol;
use think\facade\Log;
use Closure;

class RpcLogMiddleware
{
    /**
     * 处理RPC请求
     * @param Protocol $protocol RPC协议对象
     * @param Closure $next 下一个中间件
     * @return mixed
     */
    public function handle(Protocol $protocol, Closure $next)
    {
        $startTime = microtime(true);
        $interface = $protocol->getInterface();
        $method = $protocol->getMethod();
        $params = $protocol->getParams();
        $context = $protocol->getContext();
        
        // 记录请求开始日志
        Log::info("RPC请求开始", [
            'interface' => $interface,
            'method' => $method,
            'params' => $this->sanitizeParams($params),
            'context' => $context,
            'timestamp' => date('Y-m-d H:i:s'),
        ]);
        
        try {
            // 执行下一个中间件
            $result = $next($protocol);
            
            // 计算执行时间
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            // 记录成功日志
            Log::info("RPC请求成功", [
                'interface' => $interface,
                'method' => $method,
                'duration' => $duration . 'ms',
                'result_type' => gettype($result),
                'timestamp' => date('Y-m-d H:i:s'),
            ]);
            
            return $result;
            
        } catch (\Exception $e) {
            // 计算执行时间
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            // 记录错误日志
            Log::error("RPC请求失败", [
                'interface' => $interface,
                'method' => $method,
                'duration' => $duration . 'ms',
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'timestamp' => date('Y-m-d H:i:s'),
            ]);
            
            // 重新抛出异常
            throw $e;
        }
    }
    
    /**
     * 清理敏感参数
     * @param array $params 参数数组
     * @return array
     */
    protected function sanitizeParams(array $params): array
    {
        $sensitiveKeys = ['password', 'token', 'secret', 'key'];
        
        return array_map(function ($param) use ($sensitiveKeys) {
            if (is_array($param)) {
                foreach ($sensitiveKeys as $key) {
                    if (isset($param[$key])) {
                        $param[$key] = '***';
                    }
                }
            }
            return $param;
        }, $params);
    }
}
```

### 2. 创建RPC客户端中间件

#### 客户端日志中间件

```php
<?php
// app/middleware/RpcClientLogMiddleware.php

namespace app\middleware;

use think\swoole\rpc\Protocol;
use think\facade\Log;
use Closure;

class RpcClientLogMiddleware
{
    /**
     * 处理RPC客户端请求
     * @param Protocol $protocol RPC协议对象
     * @param Closure $next 下一个中间件
     * @return mixed
     */
    public function handle(Protocol $protocol, Closure $next)
    {
        $startTime = microtime(true);
        $interface = $protocol->getInterface();
        $method = $protocol->getMethod();
        
        // 记录客户端请求日志
        Log::info("RPC客户端请求", [
            'interface' => $interface,
            'method' => $method,
            'timestamp' => date('Y-m-d H:i:s'),
        ]);
        
        try {
            // 执行RPC调用
            $result = $next($protocol);
            
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            // 记录成功日志
            Log::info("RPC客户端请求成功", [
                'interface' => $interface,
                'method' => $method,
                'duration' => $duration . 'ms',
                'timestamp' => date('Y-m-d H:i:s'),
            ]);
            
            return $result;
            
        } catch (\Exception $e) {
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            // 记录失败日志
            Log::error("RPC客户端请求失败", [
                'interface' => $interface,
                'method' => $method,
                'duration' => $duration . 'ms',
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s'),
            ]);
            
            throw $e;
        }
    }
}
```

#### 客户端重试中间件

```php
<?php
// app/middleware/RpcClientRetryMiddleware.php

namespace app\middleware;

use think\swoole\rpc\Protocol;
use think\swoole\exception\RpcClientException;
use think\facade\Log;
use Closure;

class RpcClientRetryMiddleware
{
    /**
     * 最大重试次数
     * @var int
     */
    protected $maxRetries = 3;
    
    /**
     * 重试间隔（毫秒）
     * @var int
     */
    protected $retryDelay = 1000;
    
    /**
     * 处理RPC客户端请求
     * @param Protocol $protocol RPC协议对象
     * @param Closure $next 下一个中间件
     * @return mixed
     */
    public function handle(Protocol $protocol, Closure $next)
    {
        $attempts = 0;
        $lastException = null;
        
        while ($attempts < $this->maxRetries) {
            try {
                return $next($protocol);
                
            } catch (RpcClientException $e) {
                $attempts++;
                $lastException = $e;
                
                // 判断是否应该重试
                if (!$this->shouldRetry($e) || $attempts >= $this->maxRetries) {
                    break;
                }
                
                // 记录重试日志
                Log::warning("RPC客户端请求失败，准备重试", [
                    'interface' => $protocol->getInterface(),
                    'method' => $protocol->getMethod(),
                    'attempt' => $attempts,
                    'error' => $e->getMessage(),
                ]);
                
                // 等待后重试
                if ($this->retryDelay > 0) {
                    usleep($this->retryDelay * 1000);
                }
            }
        }
        
        // 所有重试都失败，抛出最后一个异常
        throw $lastException;
    }
    
    /**
     * 判断是否应该重试
     * @param RpcClientException $e 异常对象
     * @return bool
     */
    protected function shouldRetry(RpcClientException $e): bool
    {
        // 定义可重试的错误类型
        $retryableErrors = [
            'Connection refused',
            'Connection timeout',
            'Network unreachable',
        ];
        
        $message = $e->getMessage();
        
        foreach ($retryableErrors as $error) {
            if (strpos($message, $error) !== false) {
                return true;
            }
        }
        
        return false;
    }
}
```

### 3. 创建WebSocket中间件

#### WebSocket认证中间件

```php
<?php
// app/middleware/WebSocketAuthMiddleware.php

namespace app\middleware;

use think\Request;
use think\swoole\Websocket;
use Closure;

class WebSocketAuthMiddleware
{
    /**
     * 处理WebSocket连接
     * @param Request $request HTTP请求对象
     * @param Closure $next 下一个中间件
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 获取认证token
        $token = $request->param('token', '');
        
        if (empty($token)) {
            // 没有token，拒绝连接
            throw new \Exception('缺少认证token', 401);
        }
        
        // 验证token
        $userInfo = $this->validateToken($token);
        
        if (!$userInfo) {
            throw new \Exception('无效的认证token', 401);
        }
        
        // 将用户信息添加到请求中
        $request->withAttribute('user', $userInfo);
        
        return $next($request);
    }
    
    /**
     * 验证token并获取用户信息
     * @param string $token
     * @return array|false
     */
    protected function validateToken(string $token)
    {
        try {
            // 从缓存或数据库验证token
            $tokenData = cache("websocket_token_{$token}");
            
            if (!$tokenData || $tokenData['expires'] < time()) {
                return false;
            }
            
            return [
                'id' => $tokenData['user_id'],
                'username' => $tokenData['username'],
                'roles' => $tokenData['roles'] ?? [],
            ];
            
        } catch (\Exception $e) {
            return false;
        }
    }
}
```

### 4. 高级中间件示例

#### 限流中间件

```php
<?php
// app/middleware/RateLimitMiddleware.php

namespace app\middleware;

use think\swoole\rpc\Protocol;
use think\facade\Cache;
use Closure;

class RateLimitMiddleware
{
    /**
     * 每分钟最大请求数
     * @var int
     */
    protected $maxRequests = 60;
    
    /**
     * 时间窗口（秒）
     * @var int
     */
    protected $timeWindow = 60;
    
    /**
     * 处理请求
     * @param Protocol $protocol RPC协议对象
     * @param Closure $next 下一个中间件
     * @return mixed
     */
    public function handle(Protocol $protocol, Closure $next)
    {
        $key = $this->getRateLimitKey($protocol);
        $current = Cache::get($key, 0);
        
        if ($current >= $this->maxRequests) {
            throw new \Exception('请求频率过高，请稍后再试', 429);
        }
        
        // 增加计数
        Cache::set($key, $current + 1, $this->timeWindow);
        
        return $next($protocol);
    }
    
    /**
     * 获取限流键名
     * @param Protocol $protocol
     * @return string
     */
    protected function getRateLimitKey(Protocol $protocol): string
    {
        $context = $protocol->getContext();
        $userId = $context['user']['id'] ?? 'anonymous';
        $interface = $protocol->getInterface();
        $method = $protocol->getMethod();
        
        return "rate_limit:{$userId}:{$interface}:{$method}:" . floor(time() / $this->timeWindow);
    }
}
```

#### 缓存中间件

```php
<?php
// app/middleware/CacheMiddleware.php

namespace app\middleware;

use think\swoole\rpc\Protocol;
use think\facade\Cache;
use Closure;

class CacheMiddleware
{
    /**
     * 缓存过期时间（秒）
     * @var int
     */
    protected $ttl = 300;
    
    /**
     * 处理请求
     * @param Protocol $protocol RPC协议对象
     * @param Closure $next 下一个中间件
     * @return mixed
     */
    public function handle(Protocol $protocol, Closure $next)
    {
        // 只对查询类方法启用缓存
        if (!$this->shouldCache($protocol)) {
            return $next($protocol);
        }
        
        $cacheKey = $this->getCacheKey($protocol);
        
        // 尝试从缓存获取结果
        $cached = Cache::get($cacheKey);
        if ($cached !== null) {
            return $cached;
        }
        
        // 执行实际方法
        $result = $next($protocol);
        
        // 缓存结果
        Cache::set($cacheKey, $result, $this->ttl);
        
        return $result;
    }
    
    /**
     * 判断是否应该缓存
     * @param Protocol $protocol
     * @return bool
     */
    protected function shouldCache(Protocol $protocol): bool
    {
        $method = $protocol->getMethod();
        
        // 只缓存查询类方法
        $cacheableMethods = ['get', 'find', 'list', 'search', 'query'];
        
        foreach ($cacheableMethods as $prefix) {
            if (strpos(strtolower($method), $prefix) === 0) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 生成缓存键名
     * @param Protocol $protocol
     * @return string
     */
    protected function getCacheKey(Protocol $protocol): string
    {
        $interface = $protocol->getInterface();
        $method = $protocol->getMethod();
        $params = $protocol->getParams();
        
        return 'rpc_cache:' . md5($interface . ':' . $method . ':' . serialize($params));
    }
}
```

## 注意事项

### 1. 中间件顺序

- 中间件按配置顺序执行
- 认证中间件应该在业务中间件之前
- 日志中间件通常放在最外层

### 2. 异常处理

- 中间件中的异常会中断执行链
- 使用try-catch处理可恢复的错误
- 记录详细的错误信息用于调试

### 3. 性能考虑

- 避免在中间件中执行耗时操作
- 合理使用缓存减少重复计算
- 监控中间件的执行时间

### 4. 上下文传递

- 使用Protocol的context传递数据
- 避免修改原始请求参数
- 确保上下文数据的类型安全

### 5. 测试和调试

- 为每个中间件编写单元测试
- 使用日志记录中间件执行过程
- 提供开发环境的调试信息

## 常见问题

### Q: 中间件执行顺序如何控制？
A: 中间件按照配置数组的顺序执行，先配置的先执行。

### Q: 如何在中间件间传递数据？
A: 使用Protocol的context属性或Request的attribute属性传递数据。

### Q: 中间件异常如何处理？
A: 中间件中的异常会中断执行链，可以使用try-catch捕获并处理。

### Q: 如何跳过某些中间件？
A: 可以在中间件内部根据条件判断是否执行逻辑，或使用条件配置。
