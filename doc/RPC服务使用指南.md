# RPC服务使用指南

## 功能概述

RPC（Remote Procedure Call）服务是基于Swoole协程实现的高性能远程过程调用功能，支持服务端和客户端的双向通信。该功能提供了完整的RPC解决方案，包括服务注册、接口代理、连接池管理、中间件支持等特性。

## 适用场景

- 微服务架构中的服务间通信
- 分布式系统的远程方法调用
- 高并发场景下的异步通信
- 需要负载均衡和连接复用的场景

## 配置说明

### 基础配置

在 `config/swoole.php` 文件中配置RPC服务：

```php
'rpc' => [
    // RPC服务端配置
    'server' => [
        'enable' => true,                    // 是否启用RPC服务端
        'host' => '0.0.0.0',                // 监听地址
        'port' => 9090,                     // 监听端口
        'worker_num' => 1,                  // 工作进程数
        'middleware' => [                   // 服务端中间件
            \think\swoole\middleware\TraceRpcServer::class
        ],
        'services' => [                     // 注册的服务列表
            \app\rpc\UserService::class,
            \app\rpc\OrderService::class,
        ],
    ],
    // RPC客户端配置
    'client' => [
        'user' => [                         // 客户端连接名称
            'host' => '127.0.0.1',          // 服务端地址
            'port' => 9090,                 // 服务端端口
            'max_active' => 20,             // 最大活动连接数
            'max_wait_time' => 1,           // 最大等待时间（秒）
            'parser' => \think\swoole\rpc\JsonParser::class, // 数据解析器
            'tries' => 2,                   // 重试次数
            'middleware' => [               // 客户端中间件
                \think\swoole\middleware\TraceRpcClient::class
            ],
        ],
    ],
],
```

### 配置项详解

#### 服务端配置项

- `enable`: 是否启用RPC服务端，默认false
- `host`: 服务监听地址，默认'0.0.0.0'
- `port`: 服务监听端口，默认9090
- `worker_num`: 工作进程数量，默认1
- `middleware`: 服务端中间件数组，用于请求处理前后的逻辑
- `services`: 注册的RPC服务类列表

#### 客户端配置项

- `host`: RPC服务端地址
- `port`: RPC服务端端口
- `max_active`: 连接池最大活动连接数
- `max_wait_time`: 获取连接的最大等待时间
- `parser`: 数据解析器类，默认JsonParser
- `tries`: 请求失败时的重试次数
- `middleware`: 客户端中间件数组

## 使用方法

### 1. 创建RPC服务

#### 定义服务接口

```php
<?php
// app/rpc/contract/UserServiceInterface.php

namespace app\rpc\contract;

interface UserServiceInterface
{
    /**
     * 获取用户信息
     * @param int $userId 用户ID
     * @return array 用户信息
     */
    public function getUserInfo(int $userId): array;
    
    /**
     * 创建用户
     * @param array $userData 用户数据
     * @return int 用户ID
     */
    public function createUser(array $userData): int;
    
    /**
     * 更新用户信息
     * @param int $userId 用户ID
     * @param array $userData 更新的用户数据
     * @return bool 是否成功
     */
    public function updateUser(int $userId, array $userData): bool;
}
```

#### 实现服务类

```php
<?php
// app/rpc/UserService.php

namespace app\rpc;

use app\rpc\contract\UserServiceInterface;
use think\facade\Db;

class UserService implements UserServiceInterface
{
    /**
     * 获取用户信息
     * @param int $userId 用户ID
     * @return array 用户信息
     */
    public function getUserInfo(int $userId): array
    {
        // 从数据库获取用户信息
        $user = Db::table('users')->where('id', $userId)->find();
        
        if (!$user) {
            throw new \Exception("用户不存在: {$userId}");
        }
        
        return [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'created_at' => $user['created_at'],
        ];
    }
    
    /**
     * 创建用户
     * @param array $userData 用户数据
     * @return int 用户ID
     */
    public function createUser(array $userData): int
    {
        // 验证必要字段
        if (empty($userData['username']) || empty($userData['email'])) {
            throw new \Exception('用户名和邮箱不能为空');
        }
        
        // 检查用户名是否已存在
        $exists = Db::table('users')
            ->where('username', $userData['username'])
            ->find();
            
        if ($exists) {
            throw new \Exception('用户名已存在');
        }
        
        // 插入用户数据
        $userId = Db::table('users')->insertGetId([
            'username' => $userData['username'],
            'email' => $userData['email'],
            'password' => password_hash($userData['password'], PASSWORD_DEFAULT),
            'created_at' => date('Y-m-d H:i:s'),
        ]);
        
        return $userId;
    }
    
    /**
     * 更新用户信息
     * @param int $userId 用户ID
     * @param array $userData 更新的用户数据
     * @return bool 是否成功
     */
    public function updateUser(int $userId, array $userData): bool
    {
        // 检查用户是否存在
        $user = Db::table('users')->where('id', $userId)->find();
        if (!$user) {
            throw new \Exception("用户不存在: {$userId}");
        }
        
        // 准备更新数据
        $updateData = [];
        if (isset($userData['username'])) {
            $updateData['username'] = $userData['username'];
        }
        if (isset($userData['email'])) {
            $updateData['email'] = $userData['email'];
        }
        if (isset($userData['password'])) {
            $updateData['password'] = password_hash($userData['password'], PASSWORD_DEFAULT);
        }
        
        if (empty($updateData)) {
            return true; // 没有需要更新的数据
        }
        
        $updateData['updated_at'] = date('Y-m-d H:i:s');
        
        // 执行更新
        $result = Db::table('users')
            ->where('id', $userId)
            ->update($updateData);
            
        return $result > 0;
    }
}
```

### 2. 启动RPC服务

```bash
# 启动Swoole服务（包含RPC服务）
php think swoole
```

### 3. 生成客户端接口

使用命令生成RPC客户端接口文件：

```bash
# 生成RPC接口文件
php think rpc:interface
```

这将在 `app/rpc.php` 文件中生成客户端接口映射：

```php
<?php
// app/rpc.php - 自动生成的文件

return [
    'user' => [
        'rpc\\contract\\user\\UserServiceInterface',
    ],
];
```

### 4. 使用RPC客户端

#### 在控制器中使用

```php
<?php
// app/controller/User.php

namespace app\controller;

use app\rpc\contract\UserServiceInterface;
use think\Request;
use think\Response;

class User
{
    /**
     * 获取用户信息
     * @param Request $request
     * @return Response
     */
    public function getUserInfo(Request $request)
    {
        $userId = $request->param('id', 0, 'intval');
        
        if ($userId <= 0) {
            return json(['code' => 400, 'msg' => '用户ID无效']);
        }
        
        try {
            // 通过依赖注入获取RPC客户端
            $userService = app(UserServiceInterface::class);
            
            // 调用远程服务方法
            $userInfo = $userService->getUserInfo($userId);
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $userInfo
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取用户信息失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 创建用户
     * @param Request $request
     * @return Response
     */
    public function createUser(Request $request)
    {
        $userData = $request->only(['username', 'email', 'password']);
        
        try {
            $userService = app(UserServiceInterface::class);
            
            // 调用远程创建用户方法
            $userId = $userService->createUser($userData);
            
            return json([
                'code' => 200,
                'msg' => '创建成功',
                'data' => ['user_id' => $userId]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '创建用户失败: ' . $e->getMessage()
            ]);
        }
    }
}
```

#### 在服务类中使用

```php
<?php
// app/service/OrderService.php

namespace app\service;

use app\rpc\contract\UserServiceInterface;

class OrderService
{
    /**
     * 创建订单
     * @param int $userId 用户ID
     * @param array $orderData 订单数据
     * @return array 订单信息
     */
    public function createOrder(int $userId, array $orderData): array
    {
        // 通过RPC获取用户信息进行验证
        $userService = app(UserServiceInterface::class);
        
        try {
            // 验证用户是否存在
            $userInfo = $userService->getUserInfo($userId);
            
            // 创建订单逻辑
            $orderId = $this->doCreateOrder($userId, $orderData);
            
            return [
                'order_id' => $orderId,
                'user_info' => $userInfo,
                'status' => 'created'
            ];
            
        } catch (\Exception $e) {
            throw new \Exception('创建订单失败: ' . $e->getMessage());
        }
    }
    
    private function doCreateOrder(int $userId, array $orderData): int
    {
        // 实际的订单创建逻辑
        // ...
        return 12345; // 返回订单ID
    }
}
```

## 示例代码

### 完整的文件上传RPC服务示例

```php
<?php
// app/rpc/FileService.php

namespace app\rpc;

use think\File;
use think\facade\Filesystem;

class FileService
{
    /**
     * 上传文件
     * @param File $file 文件对象
     * @param string $path 存储路径
     * @return array 文件信息
     */
    public function uploadFile(File $file, string $path = 'uploads'): array
    {
        try {
            // 验证文件
            if (!$file->isValid()) {
                throw new \Exception('文件无效');
            }
            
            // 生成文件名
            $filename = date('Ymd') . '/' . uniqid() . '.' . $file->extension();
            $fullPath = $path . '/' . $filename;
            
            // 存储文件
            $result = Filesystem::disk('public')->putFileAs(
                $path . '/' . date('Ymd'),
                $file,
                uniqid() . '.' . $file->extension()
            );
            
            if (!$result) {
                throw new \Exception('文件存储失败');
            }
            
            return [
                'filename' => $filename,
                'original_name' => $file->getOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'url' => '/storage/' . $result,
                'uploaded_at' => date('Y-m-d H:i:s')
            ];
            
        } catch (\Exception $e) {
            throw new \Exception('文件上传失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 删除文件
     * @param string $filename 文件名
     * @return bool 是否成功
     */
    public function deleteFile(string $filename): bool
    {
        try {
            return Filesystem::disk('public')->delete($filename);
        } catch (\Exception $e) {
            throw new \Exception('文件删除失败: ' . $e->getMessage());
        }
    }
}
```

## 注意事项

### 1. 服务注册

- 所有RPC服务类必须在配置文件的 `services` 数组中注册
- 服务类必须实现对应的接口
- 接口方法的参数和返回值类型要明确定义

### 2. 错误处理

- RPC调用可能抛出 `RpcClientException` 或 `RpcResponseException`
- 建议使用try-catch包装RPC调用
- 服务端抛出的异常会传递到客户端

### 3. 性能优化

- 合理配置连接池参数，避免连接数过多或过少
- 使用中间件进行请求日志记录和性能监控
- 对于高频调用的方法，考虑添加缓存

### 4. 安全考虑

- 在生产环境中限制RPC服务的访问IP
- 使用中间件进行身份验证和权限控制
- 对敏感数据进行加密传输

### 5. 调试技巧

- 启用链路跟踪中间件进行请求追踪
- 查看Swoole服务日志定位问题
- 使用 `var_dump` 或日志记录调试信息

## 常见问题

### Q: RPC调用超时怎么办？
A: 检查网络连接，调整 `max_wait_time` 参数，或增加重试次数。

### Q: 如何实现负载均衡？
A: 可以配置多个客户端连接到不同的服务端实例，或使用外部负载均衡器。

### Q: 支持哪些数据类型？
A: 支持PHP基本数据类型、数组、对象，以及 `think\File` 文件对象。

### Q: 如何监控RPC服务状态？
A: 使用链路跟踪中间件，或自定义中间件记录请求统计信息。
