# 队列处理使用指南

## 功能概述

队列处理功能基于ThinkPHP的队列系统和Swoole协程实现，提供了高性能的异步任务处理能力。该功能支持多种队列驱动、工作进程管理、任务重试、失败处理等特性，适用于处理耗时任务、批量数据处理、定时任务等场景。

## 适用场景

- 邮件发送
- 图片处理和文件上传
- 数据导入导出
- 报表生成
- 消息推送
- 定时任务处理
- 批量数据处理

## 配置说明

### 基础配置

在 `config/swoole.php` 文件中配置队列服务：

```php
'queue' => [
    'enable' => true,                       // 是否启用队列服务
    'workers' => [                          // 工作进程配置
        'email' => [                        // 邮件队列
            'connection' => 'redis',        // 队列连接名称
            'queue' => 'email',             // 队列名称
            'worker_num' => 2,              // 工作进程数量
            'delay' => 0,                   // 延迟时间（秒）
            'sleep' => 3,                   // 空闲时休眠时间（秒）
            'tries' => 3,                   // 最大重试次数
            'timeout' => 60,                // 任务超时时间（秒）
        ],
        'image' => [                        // 图片处理队列
            'connection' => 'redis',
            'queue' => 'image_process',
            'worker_num' => 1,
            'delay' => 0,
            'sleep' => 5,
            'tries' => 2,
            'timeout' => 120,
        ],
        'report' => [                       // 报表生成队列
            'connection' => 'database',
            'queue' => 'report_generate',
            'worker_num' => 1,
            'delay' => 0,
            'sleep' => 10,
            'tries' => 1,
            'timeout' => 300,
        ],
    ],
],
```

### 队列连接配置

在 `config/queue.php` 文件中配置队列连接：

```php
return [
    // 默认队列连接
    'default' => 'redis',
    
    // 队列连接配置
    'connections' => [
        // Redis队列
        'redis' => [
            'type' => 'redis',
            'host' => '127.0.0.1',
            'port' => 6379,
            'password' => '',
            'select' => 0,
            'timeout' => 0,
            'persistent' => false,
        ],
        
        // 数据库队列
        'database' => [
            'type' => 'database',
            'queue' => 'default',
            'table' => 'jobs',
            'connection' => null,
        ],
        
        // 同步队列（用于测试）
        'sync' => [
            'type' => 'sync',
        ],
    ],
    
    // 失败任务配置
    'failed' => [
        'type' => 'database',
        'database' => 'mysql',
        'table' => 'failed_jobs',
    ],
];
```

### 配置项详解

#### 队列工作进程配置

- `connection`: 队列连接名称，对应 `config/queue.php` 中的连接
- `queue`: 队列名称，用于区分不同类型的任务
- `worker_num`: 工作进程数量，根据任务量和服务器性能调整
- `delay`: 任务延迟执行时间（秒）
- `sleep`: 队列为空时的休眠时间（秒）
- `tries`: 任务失败时的最大重试次数
- `timeout`: 单个任务的最大执行时间（秒）

## 使用方法

### 1. 创建队列任务类

#### 邮件发送任务

```php
<?php
// app/job/SendEmailJob.php

namespace app\job;

use think\queue\Job;
use think\facade\Log;

class SendEmailJob
{
    /**
     * 执行邮件发送任务
     * @param Job $job 任务对象
     * @param array $data 任务数据
     */
    public function fire(Job $job, array $data)
    {
        try {
            // 获取邮件数据
            $to = $data['to'] ?? '';
            $subject = $data['subject'] ?? '';
            $content = $data['content'] ?? '';
            $template = $data['template'] ?? '';
            
            // 验证必要参数
            if (empty($to) || empty($subject)) {
                throw new \Exception('邮件收件人和主题不能为空');
            }
            
            // 发送邮件
            $result = $this->sendEmail($to, $subject, $content, $template);
            
            if ($result) {
                // 任务执行成功，删除任务
                $job->delete();
                Log::info("邮件发送成功: {$to}");
            } else {
                // 任务执行失败，重试
                $this->handleFailure($job, '邮件发送失败');
            }
            
        } catch (\Exception $e) {
            // 异常处理
            $this->handleFailure($job, $e->getMessage());
        }
    }
    
    /**
     * 发送邮件
     * @param string $to 收件人
     * @param string $subject 主题
     * @param string $content 内容
     * @param string $template 模板
     * @return bool
     */
    private function sendEmail(string $to, string $subject, string $content, string $template = ''): bool
    {
        // 这里实现实际的邮件发送逻辑
        // 可以使用 PHPMailer、SwiftMailer 或其他邮件库
        
        try {
            // 示例：使用ThinkPHP的邮件功能
            $mail = app('mailer');
            
            if ($template) {
                // 使用模板发送
                $mail->view($template, ['content' => $content])
                     ->to($to)
                     ->subject($subject)
                     ->send();
            } else {
                // 直接发送内容
                $mail->raw($content)
                     ->to($to)
                     ->subject($subject)
                     ->send();
            }
            
            return true;
            
        } catch (\Exception $e) {
            Log::error("邮件发送异常: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 处理任务失败
     * @param Job $job 任务对象
     * @param string $error 错误信息
     */
    private function handleFailure(Job $job, string $error)
    {
        $attempts = $job->attempts();
        $maxTries = 3; // 最大重试次数
        
        if ($attempts >= $maxTries) {
            // 超过最大重试次数，标记为失败
            $job->failed();
            Log::error("邮件发送任务失败，已达到最大重试次数: {$error}");
        } else {
            // 释放任务，等待重试
            $job->release(60); // 60秒后重试
            Log::warning("邮件发送任务失败，将在60秒后重试: {$error}");
        }
    }
}
```

#### 图片处理任务

```php
<?php
// app/job/ImageProcessJob.php

namespace app\job;

use think\queue\Job;
use think\facade\Log;
use think\Image;

class ImageProcessJob
{
    /**
     * 执行图片处理任务
     * @param Job $job 任务对象
     * @param array $data 任务数据
     */
    public function fire(Job $job, array $data)
    {
        try {
            $imagePath = $data['image_path'] ?? '';
            $operations = $data['operations'] ?? [];
            $outputPath = $data['output_path'] ?? '';
            
            if (empty($imagePath) || !file_exists($imagePath)) {
                throw new \Exception('图片文件不存在');
            }
            
            // 处理图片
            $result = $this->processImage($imagePath, $operations, $outputPath);
            
            if ($result) {
                $job->delete();
                Log::info("图片处理成功: {$imagePath}");
            } else {
                $this->handleFailure($job, '图片处理失败');
            }
            
        } catch (\Exception $e) {
            $this->handleFailure($job, $e->getMessage());
        }
    }
    
    /**
     * 处理图片
     * @param string $imagePath 原图路径
     * @param array $operations 操作列表
     * @param string $outputPath 输出路径
     * @return bool
     */
    private function processImage(string $imagePath, array $operations, string $outputPath): bool
    {
        try {
            $image = Image::open($imagePath);
            
            // 执行图片操作
            foreach ($operations as $operation) {
                switch ($operation['type']) {
                    case 'resize':
                        $image->thumb(
                            $operation['width'],
                            $operation['height'],
                            $operation['type'] ?? Image::THUMB_SCALING
                        );
                        break;
                        
                    case 'crop':
                        $image->crop(
                            $operation['width'],
                            $operation['height'],
                            $operation['x'] ?? 0,
                            $operation['y'] ?? 0
                        );
                        break;
                        
                    case 'rotate':
                        $image->rotate($operation['angle'] ?? 90);
                        break;
                        
                    case 'watermark':
                        $image->water(
                            $operation['watermark'],
                            $operation['position'] ?? Image::WATER_SOUTHEAST,
                            $operation['alpha'] ?? 80
                        );
                        break;
                }
            }
            
            // 保存处理后的图片
            $image->save($outputPath);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error("图片处理异常: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 处理任务失败
     * @param Job $job 任务对象
     * @param string $error 错误信息
     */
    private function handleFailure(Job $job, string $error)
    {
        $attempts = $job->attempts();
        $maxTries = 2;
        
        if ($attempts >= $maxTries) {
            $job->failed();
            Log::error("图片处理任务失败: {$error}");
        } else {
            $job->release(30);
            Log::warning("图片处理任务失败，将重试: {$error}");
        }
    }
}
```

#### 报表生成任务

```php
<?php
// app/job/ReportGenerateJob.php

namespace app\job;

use think\queue\Job;
use think\facade\Log;
use think\facade\Db;

class ReportGenerateJob
{
    /**
     * 执行报表生成任务
     * @param Job $job 任务对象
     * @param array $data 任务数据
     */
    public function fire(Job $job, array $data)
    {
        try {
            $reportType = $data['report_type'] ?? '';
            $startDate = $data['start_date'] ?? '';
            $endDate = $data['end_date'] ?? '';
            $userId = $data['user_id'] ?? 0;
            
            // 生成报表
            $result = $this->generateReport($reportType, $startDate, $endDate, $userId);
            
            if ($result) {
                $job->delete();
                Log::info("报表生成成功: {$reportType}");
            } else {
                $this->handleFailure($job, '报表生成失败');
            }
            
        } catch (\Exception $e) {
            $this->handleFailure($job, $e->getMessage());
        }
    }
    
    /**
     * 生成报表
     * @param string $reportType 报表类型
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @param int $userId 用户ID
     * @return bool
     */
    private function generateReport(string $reportType, string $startDate, string $endDate, int $userId): bool
    {
        try {
            switch ($reportType) {
                case 'sales':
                    return $this->generateSalesReport($startDate, $endDate, $userId);
                    
                case 'user':
                    return $this->generateUserReport($startDate, $endDate, $userId);
                    
                case 'order':
                    return $this->generateOrderReport($startDate, $endDate, $userId);
                    
                default:
                    throw new \Exception("未知的报表类型: {$reportType}");
            }
            
        } catch (\Exception $e) {
            Log::error("报表生成异常: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 生成销售报表
     * @param string $startDate
     * @param string $endDate
     * @param int $userId
     * @return bool
     */
    private function generateSalesReport(string $startDate, string $endDate, int $userId): bool
    {
        // 查询销售数据
        $salesData = Db::table('orders')
            ->where('status', 'completed')
            ->where('created_at', 'between', [$startDate, $endDate])
            ->field('DATE(created_at) as date, SUM(amount) as total_amount, COUNT(*) as order_count')
            ->group('DATE(created_at)')
            ->select();
        
        // 生成Excel文件
        $filename = "sales_report_{$startDate}_{$endDate}.xlsx";
        $filepath = runtime_path('reports') . $filename;
        
        // 确保目录存在
        if (!is_dir(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }
        
        // 这里使用PhpSpreadsheet或其他Excel库生成文件
        $this->createExcelFile($salesData, $filepath, '销售报表');
        
        // 更新报表记录
        Db::table('reports')->insert([
            'user_id' => $userId,
            'type' => 'sales',
            'filename' => $filename,
            'filepath' => $filepath,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'status' => 'completed',
            'created_at' => date('Y-m-d H:i:s'),
        ]);
        
        return true;
    }
    
    /**
     * 创建Excel文件
     * @param array $data 数据
     * @param string $filepath 文件路径
     * @param string $title 标题
     */
    private function createExcelFile(array $data, string $filepath, string $title)
    {
        // 这里实现Excel文件生成逻辑
        // 可以使用PhpSpreadsheet库
        
        // 示例：简单的CSV格式
        $csv = fopen($filepath, 'w');
        
        // 写入标题行
        fputcsv($csv, ['日期', '销售金额', '订单数量']);
        
        // 写入数据行
        foreach ($data as $row) {
            fputcsv($csv, [
                $row['date'],
                $row['total_amount'],
                $row['order_count']
            ]);
        }
        
        fclose($csv);
    }
    
    /**
     * 处理任务失败
     * @param Job $job 任务对象
     * @param string $error 错误信息
     */
    private function handleFailure(Job $job, string $error)
    {
        $attempts = $job->attempts();
        $maxTries = 1; // 报表生成通常不重试
        
        if ($attempts >= $maxTries) {
            $job->failed();
            Log::error("报表生成任务失败: {$error}");
        } else {
            $job->release(120);
            Log::warning("报表生成任务失败，将重试: {$error}");
        }
    }
}
```

### 2. 推送任务到队列

#### 在控制器中推送任务

```php
<?php
// app/controller/Email.php

namespace app\controller;

use think\Request;
use think\Response;
use think\facade\Queue;

class Email
{
    /**
     * 发送邮件
     * @param Request $request
     * @return Response
     */
    public function send(Request $request)
    {
        $to = $request->param('to');
        $subject = $request->param('subject');
        $content = $request->param('content');
        $template = $request->param('template', '');
        
        // 验证参数
        if (empty($to) || empty($subject)) {
            return json(['code' => 400, 'msg' => '收件人和主题不能为空']);
        }
        
        try {
            // 推送邮件任务到队列
            $jobId = Queue::push(
                'app\\job\\SendEmailJob',           // 任务类
                [                                   // 任务数据
                    'to' => $to,
                    'subject' => $subject,
                    'content' => $content,
                    'template' => $template,
                ],
                'email'                             // 队列名称
            );
            
            return json([
                'code' => 200,
                'msg' => '邮件已加入发送队列',
                'data' => ['job_id' => $jobId]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '邮件推送失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 批量发送邮件
     * @param Request $request
     * @return Response
     */
    public function batchSend(Request $request)
    {
        $emails = $request->param('emails', []); // 邮件列表
        $subject = $request->param('subject');
        $content = $request->param('content');
        
        if (empty($emails) || empty($subject)) {
            return json(['code' => 400, 'msg' => '邮件列表和主题不能为空']);
        }
        
        try {
            $jobIds = [];
            
            // 批量推送邮件任务
            foreach ($emails as $email) {
                $jobId = Queue::push(
                    'app\\job\\SendEmailJob',
                    [
                        'to' => $email,
                        'subject' => $subject,
                        'content' => $content,
                    ],
                    'email'
                );
                $jobIds[] = $jobId;
            }
            
            return json([
                'code' => 200,
                'msg' => '批量邮件已加入发送队列',
                'data' => [
                    'total' => count($jobIds),
                    'job_ids' => $jobIds
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '批量邮件推送失败: ' . $e->getMessage()
            ]);
        }
    }
}
```

#### 延迟任务推送

```php
<?php
// app/controller/Report.php

namespace app\controller;

use think\Request;
use think\Response;
use think\facade\Queue;

class Report
{
    /**
     * 生成报表
     * @param Request $request
     * @return Response
     */
    public function generate(Request $request)
    {
        $reportType = $request->param('report_type');
        $startDate = $request->param('start_date');
        $endDate = $request->param('end_date');
        $delay = $request->param('delay', 0); // 延迟时间（秒）
        
        if (empty($reportType) || empty($startDate) || empty($endDate)) {
            return json(['code' => 400, 'msg' => '参数不完整']);
        }
        
        try {
            $taskData = [
                'report_type' => $reportType,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'user_id' => $request->user_id ?? 0,
            ];
            
            if ($delay > 0) {
                // 延迟任务
                $jobId = Queue::later(
                    $delay,                         // 延迟时间（秒）
                    'app\\job\\ReportGenerateJob',  // 任务类
                    $taskData,                      // 任务数据
                    'report'                        // 队列名称
                );
            } else {
                // 立即任务
                $jobId = Queue::push(
                    'app\\job\\ReportGenerateJob',
                    $taskData,
                    'report'
                );
            }
            
            return json([
                'code' => 200,
                'msg' => '报表生成任务已提交',
                'data' => ['job_id' => $jobId]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '报表任务提交失败: ' . $e->getMessage()
            ]);
        }
    }
}
```

### 3. 启动队列服务

```bash
# 启动Swoole服务（包含队列工作进程）
php think swoole
```

队列工作进程会自动启动，并开始处理队列中的任务。

## 注意事项

### 1. 任务设计

- 任务应该是幂等的，多次执行结果一致
- 避免在任务中执行长时间阻塞操作
- 合理设置任务超时时间

### 2. 错误处理

- 实现完善的异常处理机制
- 记录详细的错误日志
- 合理设置重试次数和间隔

### 3. 性能优化

- 根据任务类型调整工作进程数量
- 使用合适的队列驱动（Redis性能更好）
- 监控队列长度，避免积压

### 4. 资源管理

- 及时清理失败任务记录
- 监控内存使用情况
- 定期清理临时文件

### 5. 监控告警

- 监控队列长度和处理速度
- 设置失败任务告警
- 监控工作进程状态

## 常见问题

### Q: 队列任务执行失败怎么办？
A: 检查任务代码逻辑，查看错误日志，调整重试策略。

### Q: 如何监控队列状态？
A: 可以通过Redis命令或数据库查询监控队列长度和任务状态。

### Q: 队列积压怎么处理？
A: 增加工作进程数量，优化任务执行效率，或使用多台服务器分担负载。

### Q: 如何实现任务优先级？
A: 使用不同的队列名称，为重要任务分配更多工作进程。
