>rpc服务支持了动态中间件功能  比如多个方法可能有公共的参数可以放到context里 然后使用中间件统一处理


```php

class User
{
    use WithMiddleware;

    protected $user;

    public function __construct()
    {
        $this->middleware(function (Protocol $protocol, $next) {
            $context = $protocol->getContext();
            $this->user = User::find($context['id']);
            return $next($protocol);
        });
    }

    public function update(array $data)
    {
        $this->user->save($data);
    }

    public function delete()
    {
        $this->user->delete();
    }
}

```

**那么现在就可以把这个id参数 放到context里 通过中间件 统一处理**

> Rpc调用的时候需要和以前不同


```php

$rpc->withContext([])->update(['xxxx']);

```

这只是中间件的一种用法，中间件还可以做其他的事，自己发挥。

protocol对象里有这条rpc请求的所有信息。
