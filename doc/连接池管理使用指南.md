# 连接池管理使用指南

## 功能概述

连接池管理是基于Swoole协程实现的高性能连接复用机制，支持数据库连接池、缓存连接池、自定义连接池等多种类型。该功能通过预创建和复用连接，显著提升了高并发场景下的性能表现，减少了连接建立和销毁的开销。

## 适用场景

- 高并发Web应用
- 微服务架构
- 数据库密集型应用
- 缓存频繁访问场景
- 第三方API调用
- 消息队列连接管理

## 配置说明

### 基础配置

在 `config/swoole.php` 文件中配置连接池：

```php
'pool' => [
    // 数据库连接池
    'db' => [
        'enable' => true,                   // 是否启用数据库连接池
        'max_active' => 10,                 // 最大活动连接数
        'max_wait_time' => 1,               // 最大等待时间（秒）
        'max_idle_time' => 20,              // 最大空闲时间（秒）
        'min_active' => 0,                  // 最小活动连接数
    ],
    
    // 缓存连接池
    'cache' => [
        'enable' => true,
        'max_active' => 20,
        'max_wait_time' => 1,
        'max_idle_time' => 20,
        'min_active' => 0,
    ],
    
    // 自定义连接池
    'redis' => [
        'type' => \app\pool\RedisConnector::class,  // 连接器类
        'max_active' => 15,
        'max_wait_time' => 2,
        'max_idle_time' => 30,
        'min_active' => 1,
        // 连接器特定配置
        'host' => '127.0.0.1',
        'port' => 6379,
        'password' => '',
        'database' => 0,
    ],
    
    // HTTP客户端连接池
    'http_client' => [
        'type' => \app\pool\HttpClientConnector::class,
        'max_active' => 5,
        'max_wait_time' => 3,
        'max_idle_time' => 60,
        'timeout' => 30,
        'verify_ssl' => false,
    ],
],
```

### 配置项详解

#### 通用配置项

- `enable`: 是否启用连接池，默认true
- `max_active`: 最大活动连接数，超过此数量的请求将等待
- `max_wait_time`: 获取连接的最大等待时间（秒）
- `max_idle_time`: 连接最大空闲时间，超过将被回收
- `min_active`: 最小活动连接数，连接池初始化时创建的连接数

#### 自定义连接池配置

- `type`: 连接器类名，必须实现 `ConnectorInterface` 接口
- 其他配置项根据具体连接器的需求定义

## 使用方法

### 1. 使用内置连接池

#### 数据库连接池使用

```php
<?php
// app/controller/User.php

namespace app\controller;

use think\facade\Db;
use think\Request;
use think\Response;

class User
{
    /**
     * 获取用户列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            // 直接使用Db门面，自动使用连接池
            $users = Db::table('users')
                ->field('id, username, email, created_at')
                ->where('status', 1)
                ->limit(20)
                ->select();
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $users
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 创建用户（事务示例）
     * @param Request $request
     * @return Response
     */
    public function create(Request $request)
    {
        $userData = $request->only(['username', 'email', 'password']);
        
        try {
            // 使用事务，连接池会自动管理连接
            Db::transaction(function () use ($userData) {
                // 检查用户名是否存在
                $exists = Db::table('users')
                    ->where('username', $userData['username'])
                    ->find();
                
                if ($exists) {
                    throw new \Exception('用户名已存在');
                }
                
                // 创建用户
                $userId = Db::table('users')->insertGetId([
                    'username' => $userData['username'],
                    'email' => $userData['email'],
                    'password' => password_hash($userData['password'], PASSWORD_DEFAULT),
                    'created_at' => date('Y-m-d H:i:s'),
                ]);
                
                // 创建用户配置
                Db::table('user_profiles')->insert([
                    'user_id' => $userId,
                    'nickname' => $userData['username'],
                    'created_at' => date('Y-m-d H:i:s'),
                ]);
                
                return $userId;
            });
            
            return json([
                'code' => 200,
                'msg' => '创建成功'
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '创建失败: ' . $e->getMessage()
            ]);
        }
    }
}
```

#### 缓存连接池使用

```php
<?php
// app/service/CacheService.php

namespace app\service;

use think\facade\Cache;

class CacheService
{
    /**
     * 获取用户缓存信息
     * @param int $userId
     * @return array|null
     */
    public function getUserCache(int $userId): ?array
    {
        try {
            // 直接使用Cache门面，自动使用连接池
            $cacheKey = "user_info_{$userId}";
            $userInfo = Cache::get($cacheKey);
            
            if (!$userInfo) {
                // 从数据库获取并缓存
                $userInfo = $this->getUserFromDatabase($userId);
                if ($userInfo) {
                    Cache::set($cacheKey, $userInfo, 3600); // 缓存1小时
                }
            }
            
            return $userInfo;
            
        } catch (\Exception $e) {
            // 缓存失败时记录日志但不影响业务
            trace("缓存操作失败: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 批量设置缓存
     * @param array $data 键值对数组
     * @param int $ttl 过期时间
     * @return bool
     */
    public function setBatch(array $data, int $ttl = 3600): bool
    {
        try {
            foreach ($data as $key => $value) {
                Cache::set($key, $value, $ttl);
            }
            return true;
            
        } catch (\Exception $e) {
            trace("批量缓存设置失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 从数据库获取用户信息
     * @param int $userId
     * @return array|null
     */
    private function getUserFromDatabase(int $userId): ?array
    {
        return \think\facade\Db::table('users')
            ->where('id', $userId)
            ->find();
    }
}
```

### 2. 创建自定义连接池

#### Redis连接器

```php
<?php
// app/pool/RedisConnector.php

namespace app\pool;

use Smf\ConnectionPool\Connectors\ConnectorInterface;
use Swoole\Coroutine\Redis;

class RedisConnector implements ConnectorInterface
{
    /**
     * @var array 连接配置
     */
    protected $config;
    
    /**
     * 构造函数
     * @param array $config 连接配置
     */
    public function __construct(array $config)
    {
        $this->config = $config;
    }
    
    /**
     * 创建连接
     * @return Redis
     */
    public function connect()
    {
        $redis = new Redis();
        
        // 连接Redis服务器
        $connected = $redis->connect(
            $this->config['host'] ?? '127.0.0.1',
            $this->config['port'] ?? 6379,
            $this->config['timeout'] ?? 1
        );
        
        if (!$connected) {
            throw new \Exception('Redis连接失败');
        }
        
        // 认证
        if (!empty($this->config['password'])) {
            $redis->auth($this->config['password']);
        }
        
        // 选择数据库
        if (isset($this->config['database'])) {
            $redis->select($this->config['database']);
        }
        
        return $redis;
    }
    
    /**
     * 断开连接
     * @param Redis $connection 连接对象
     */
    public function disconnect($connection)
    {
        if ($connection instanceof Redis) {
            $connection->close();
        }
    }
    
    /**
     * 检查连接是否有效
     * @param Redis $connection 连接对象
     * @return bool
     */
    public function isConnected($connection): bool
    {
        if (!$connection instanceof Redis) {
            return false;
        }
        
        try {
            // 发送ping命令检查连接
            return $connection->ping() === 'PONG';
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 重置连接状态
     * @param Redis $connection 连接对象
     */
    public function reset($connection)
    {
        // Redis连接通常不需要重置
        // 如果需要，可以在这里实现重置逻辑
    }
    
    /**
     * 验证连接配置
     * @param array $config 配置数组
     * @return bool
     */
    public function validate(array $config): bool
    {
        return isset($config['host']) && isset($config['port']);
    }
}
```

#### HTTP客户端连接器

```php
<?php
// app/pool/HttpClientConnector.php

namespace app\pool;

use Smf\ConnectionPool\Connectors\ConnectorInterface;
use Swoole\Coroutine\Http\Client;

class HttpClientConnector implements ConnectorInterface
{
    /**
     * @var array 连接配置
     */
    protected $config;
    
    public function __construct(array $config)
    {
        $this->config = $config;
    }
    
    /**
     * 创建HTTP客户端连接
     * @return Client
     */
    public function connect()
    {
        $host = $this->config['host'] ?? 'localhost';
        $port = $this->config['port'] ?? 80;
        $ssl = $this->config['ssl'] ?? false;
        
        $client = new Client($host, $port, $ssl);
        
        // 设置超时时间
        $client->set([
            'timeout' => $this->config['timeout'] ?? 30,
            'keep_alive' => true,
        ]);
        
        return $client;
    }
    
    /**
     * 断开连接
     * @param Client $connection
     */
    public function disconnect($connection)
    {
        if ($connection instanceof Client) {
            $connection->close();
        }
    }
    
    /**
     * 检查连接是否有效
     * @param Client $connection
     * @return bool
     */
    public function isConnected($connection): bool
    {
        return $connection instanceof Client && $connection->connected;
    }
    
    /**
     * 重置连接
     * @param Client $connection
     */
    public function reset($connection)
    {
        // HTTP客户端通常不需要重置
    }
    
    /**
     * 验证配置
     * @param array $config
     * @return bool
     */
    public function validate(array $config): bool
    {
        return isset($config['host']);
    }
}
```

### 3. 使用自定义连接池

#### Redis连接池使用

```php
<?php
// app/service/RedisService.php

namespace app\service;

use think\swoole\Pool;

class RedisService
{
    /**
     * @var Pool 连接池管理器
     */
    protected $pool;
    
    public function __construct(Pool $pool)
    {
        $this->pool = $pool;
    }
    
    /**
     * 设置缓存
     * @param string $key 键名
     * @param mixed $value 值
     * @param int $ttl 过期时间
     * @return bool
     */
    public function set(string $key, $value, int $ttl = 3600): bool
    {
        // 获取Redis连接池
        $redisPool = $this->pool->get('redis');
        
        // 从连接池获取连接
        $redis = $redisPool->get();
        
        try {
            // 执行Redis操作
            $result = $redis->setex($key, $ttl, serialize($value));
            return $result === true;
            
        } finally {
            // 归还连接到连接池
            $redisPool->put($redis);
        }
    }
    
    /**
     * 获取缓存
     * @param string $key 键名
     * @return mixed
     */
    public function get(string $key)
    {
        $redisPool = $this->pool->get('redis');
        $redis = $redisPool->get();
        
        try {
            $value = $redis->get($key);
            return $value ? unserialize($value) : null;
            
        } finally {
            $redisPool->put($redis);
        }
    }
    
    /**
     * 批量操作示例
     * @param array $data 数据数组
     * @return bool
     */
    public function mset(array $data): bool
    {
        $redisPool = $this->pool->get('redis');
        $redis = $redisPool->get();
        
        try {
            // 开始事务
            $redis->multi();
            
            foreach ($data as $key => $value) {
                $redis->set($key, serialize($value));
            }
            
            // 执行事务
            $results = $redis->exec();
            
            return !in_array(false, $results, true);
            
        } finally {
            $redisPool->put($redis);
        }
    }
    
    /**
     * 使用管道操作
     * @param array $commands 命令数组
     * @return array
     */
    public function pipeline(array $commands): array
    {
        $redisPool = $this->pool->get('redis');
        $redis = $redisPool->get();
        
        try {
            // 开始管道
            $pipe = $redis->multi(\Redis::PIPELINE);
            
            foreach ($commands as $command) {
                $method = array_shift($command);
                call_user_func_array([$pipe, $method], $command);
            }
            
            // 执行管道
            return $pipe->exec();
            
        } finally {
            $redisPool->put($redis);
        }
    }
}
```

#### HTTP客户端连接池使用

```php
<?php
// app/service/HttpService.php

namespace app\service;

use think\swoole\Pool;
use Swoole\Coroutine\Http\Client;

class HttpService
{
    /**
     * @var Pool 连接池管理器
     */
    protected $pool;
    
    public function __construct(Pool $pool)
    {
        $this->pool = $pool;
    }
    
    /**
     * 发送GET请求
     * @param string $path 请求路径
     * @param array $params 查询参数
     * @param array $headers 请求头
     * @return array
     */
    public function get(string $path, array $params = [], array $headers = []): array
    {
        $httpPool = $this->pool->get('http_client');
        $client = $httpPool->get();
        
        try {
            // 设置请求头
            if (!empty($headers)) {
                $client->setHeaders($headers);
            }
            
            // 构建查询字符串
            if (!empty($params)) {
                $path .= '?' . http_build_query($params);
            }
            
            // 发送GET请求
            $client->get($path);
            
            return [
                'status_code' => $client->statusCode,
                'headers' => $client->headers,
                'body' => $client->body,
            ];
            
        } finally {
            $httpPool->put($client);
        }
    }
    
    /**
     * 发送POST请求
     * @param string $path 请求路径
     * @param array $data 请求数据
     * @param array $headers 请求头
     * @return array
     */
    public function post(string $path, array $data = [], array $headers = []): array
    {
        $httpPool = $this->pool->get('http_client');
        $client = $httpPool->get();
        
        try {
            // 设置请求头
            $headers['Content-Type'] = $headers['Content-Type'] ?? 'application/json';
            $client->setHeaders($headers);
            
            // 发送POST请求
            $client->post($path, json_encode($data));
            
            return [
                'status_code' => $client->statusCode,
                'headers' => $client->headers,
                'body' => $client->body,
            ];
            
        } finally {
            $httpPool->put($client);
        }
    }
    
    /**
     * 批量请求
     * @param array $requests 请求列表
     * @return array
     */
    public function batch(array $requests): array
    {
        $results = [];
        
        foreach ($requests as $index => $request) {
            $method = $request['method'] ?? 'GET';
            $path = $request['path'] ?? '/';
            $data = $request['data'] ?? [];
            $headers = $request['headers'] ?? [];
            
            try {
                if (strtoupper($method) === 'POST') {
                    $results[$index] = $this->post($path, $data, $headers);
                } else {
                    $results[$index] = $this->get($path, $data, $headers);
                }
            } catch (\Exception $e) {
                $results[$index] = [
                    'error' => $e->getMessage(),
                    'status_code' => 0,
                ];
            }
        }
        
        return $results;
    }
}
```

## 注意事项

### 1. 连接池配置

- 根据实际并发量合理设置最大连接数
- 避免连接数过多导致资源浪费
- 设置合适的空闲时间避免连接泄露

### 2. 连接管理

- 使用完连接后必须归还到连接池
- 使用try-finally确保连接正确归还
- 监控连接池状态，及时发现问题

### 3. 错误处理

- 处理连接获取超时的情况
- 检查连接有效性，处理断线重连
- 记录连接池相关的错误日志

### 4. 性能优化

- 预热连接池，提前创建连接
- 监控连接池使用情况，调整配置
- 避免长时间占用连接

### 5. 资源清理

- 应用关闭时正确关闭连接池
- 定期清理无效连接
- 监控内存使用情况

## 常见问题

### Q: 连接池获取连接超时怎么办？
A: 检查最大连接数配置，增加连接数或优化业务逻辑减少连接占用时间。

### Q: 如何监控连接池状态？
A: 可以通过连接池对象的统计方法获取当前状态，或实现自定义监控。

### Q: 连接池在什么情况下会创建新连接？
A: 当池中没有可用连接且未达到最大连接数时会创建新连接。

### Q: 如何处理连接断开的情况？
A: 在连接器中实现isConnected方法检查连接状态，连接池会自动处理无效连接。
