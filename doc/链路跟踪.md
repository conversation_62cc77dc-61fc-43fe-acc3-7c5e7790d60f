# 链路跟踪使用指南

## 功能概述

链路跟踪功能基于 `topthink/think-tracing` 包实现，提供了分布式系统中请求链路的完整追踪能力。该功能支持HTTP请求、RPC调用、数据库查询等多种场景的链路跟踪，帮助开发者快速定位性能瓶颈和问题根源。

## 适用场景

- 微服务架构的请求链路追踪
- 性能瓶颈分析和优化
- 分布式系统故障排查
- API调用链路监控
- 数据库查询性能分析
- 第三方服务调用监控

## 配置说明

### 安装依赖

首先安装链路跟踪依赖包：

```bash
composer require topthink/think-tracing
```

### 基础配置

在 `config/trace.php` 文件中配置链路跟踪：

```php
<?php
// config/trace.php

return [
    // 是否启用链路跟踪
    'enable' => env('TRACE_ENABLE', false),

    // 跟踪器类型
    'type' => 'jaeger',

    // Jaeger配置
    'jaeger' => [
        'host' => env('JAEGER_HOST', '127.0.0.1'),
        'port' => env('JAEGER_PORT', 6832),
        'service_name' => env('JAEGER_SERVICE_NAME', 'swoole-app'),
    ],

    // Zipkin配置
    'zipkin' => [
        'endpoint' => env('ZIPKIN_ENDPOINT', 'http://127.0.0.1:9411/api/v2/spans'),
        'service_name' => env('ZIPKIN_SERVICE_NAME', 'swoole-app'),
    ],

    // 采样率配置
    'sampler' => [
        'type' => 'const',  // const, probabilistic, rate_limiting
        'param' => 1,       // 采样参数
    ],

    // 标签配置
    'tags' => [
        'environment' => env('APP_ENV', 'production'),
        'version' => '1.0.0',
    ],
];
```

### RPC服务端跟踪配置

在 `config/swoole.php` 文件中配置RPC服务端中间件：

```php
'rpc' => [
    'server' => [
        'enable' => true,
        'middleware' => [
            \think\swoole\middleware\TraceRpcServer::class,  // RPC服务端跟踪中间件
        ],
    ],
    'client' => [
    ],
],
```

### RPC客户端跟踪配置

```php
'rpc' => [
    'server' => [
    ],
    'client' => [
        'user' => [
            'host' => '127.0.0.1',
            'port' => 9090,
            'max_active' => 20,
            'max_wait_time' => 1,
            'middleware' => [
                \think\swoole\middleware\TraceRpcClient::class,  // RPC客户端跟踪中间件
            ],
        ],
    ],
],
```

### HTTP请求跟踪配置

在 `app/middleware.php` 文件中配置HTTP请求跟踪中间件：

```php
<?php
// app/middleware.php

return [
    \think\tracing\middleware\TraceRequests::class,  // HTTP请求跟踪中间件
];
```

## 使用方法

### 1. 启用链路跟踪

#### 环境变量配置

在 `.env` 文件中配置链路跟踪：

```bash
# 启用链路跟踪
TRACE_ENABLE=true

# Jaeger配置
JAEGER_HOST=127.0.0.1
JAEGER_PORT=6832
JAEGER_SERVICE_NAME=my-swoole-app

# 或者使用Zipkin
ZIPKIN_ENDPOINT=http://127.0.0.1:9411/api/v2/spans
ZIPKIN_SERVICE_NAME=my-swoole-app
```

#### 启动跟踪服务

```bash
# 启动Jaeger（使用Docker）
docker run -d --name jaeger \
  -p 16686:16686 \
  -p 14268:14268 \
  -p 6831:6831/udp \
  -p 6832:6832/udp \
  jaegertracing/all-in-one:latest

# 或启动Zipkin
docker run -d -p 9411:9411 openzipkin/zipkin
```

### 2. 自定义跟踪Span

#### 在业务代码中添加跟踪

```php
<?php
// app/service/UserService.php

namespace app\service;

use think\facade\Db;
use think\tracing\Tracer;

class UserService
{
    /**
     * 获取用户信息
     * @param int $userId
     * @return array|null
     */
    public function getUserInfo(int $userId): ?array
    {
        // 创建自定义Span
        $span = Tracer::startSpan('user.get_info');
        $span->setTag('user.id', $userId);

        try {
            // 数据库查询（会自动创建数据库Span）
            $user = Db::table('users')->where('id', $userId)->find();

            if (!$user) {
                $span->setTag('user.found', false);
                return null;
            }

            // 获取用户扩展信息
            $profile = $this->getUserProfile($userId);
            $user['profile'] = $profile;

            $span->setTag('user.found', true);
            $span->setTag('user.username', $user['username']);

            return $user;

        } catch (\Exception $e) {
            // 记录异常信息
            $span->setTag('error', true);
            $span->setTag('error.message', $e->getMessage());
            throw $e;

        } finally {
            // 结束Span
            $span->finish();
        }
    }

    /**
     * 获取用户配置信息
     * @param int $userId
     * @return array
     */
    private function getUserProfile(int $userId): array
    {
        $span = Tracer::startSpan('user.get_profile');
        $span->setTag('user.id', $userId);

        try {
            $profile = Db::table('user_profiles')
                ->where('user_id', $userId)
                ->find();

            return $profile ?: [];

        } finally {
            $span->finish();
        }
    }

    /**
     * 创建用户
     * @param array $userData
     * @return int
     */
    public function createUser(array $userData): int
    {
        $span = Tracer::startSpan('user.create');
        $span->setTag('user.username', $userData['username']);

        try {
            // 使用事务
            $userId = Db::transaction(function () use ($userData) {
                // 创建用户记录
                $userId = Db::table('users')->insertGetId([
                    'username' => $userData['username'],
                    'email' => $userData['email'],
                    'password' => password_hash($userData['password'], PASSWORD_DEFAULT),
                    'created_at' => date('Y-m-d H:i:s'),
                ]);

                // 创建用户配置
                Db::table('user_profiles')->insert([
                    'user_id' => $userId,
                    'nickname' => $userData['username'],
                    'created_at' => date('Y-m-d H:i:s'),
                ]);

                return $userId;
            });

            $span->setTag('user.id', $userId);
            $span->setTag('success', true);

            return $userId;

        } catch (\Exception $e) {
            $span->setTag('error', true);
            $span->setTag('error.message', $e->getMessage());
            throw $e;

        } finally {
            $span->finish();
        }
    }
}
```

### 3. RPC调用跟踪

#### RPC服务端跟踪

RPC服务端跟踪通过中间件自动实现，无需额外代码：

```php
<?php
// app/rpc/UserService.php

namespace app\rpc;

use app\service\UserService as UserServiceImpl;

class UserService
{
    protected $userService;

    public function __construct(UserServiceImpl $userService)
    {
        $this->userService = $userService;
    }

    /**
     * 获取用户信息（自动跟踪）
     * @param int $userId
     * @return array|null
     */
    public function getUserInfo(int $userId): ?array
    {
        // 这个方法调用会被TraceRpcServer中间件自动跟踪
        return $this->userService->getUserInfo($userId);
    }
}
```

#### RPC客户端跟踪

RPC客户端调用也会自动跟踪：

```php
<?php
// app/controller/User.php

namespace app\controller;

use app\rpc\contract\UserServiceInterface;
use think\Request;
use think\Response;

class User
{
    /**
     * 获取用户信息
     * @param Request $request
     * @return Response
     */
    public function show(Request $request)
    {
        $userId = $request->param('id', 0, 'intval');

        try {
            // RPC调用会被TraceRpcClient中间件自动跟踪
            $userService = app(UserServiceInterface::class);
            $userInfo = $userService->getUserInfo($userId);

            if (!$userInfo) {
                return json(['code' => 404, 'msg' => '用户不存在']);
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $userInfo
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取失败: ' . $e->getMessage()
            ]);
        }
    }
}
```

### 4. 数据库查询跟踪

数据库查询跟踪会自动启用，无需额外配置：

```php
<?php
// app/service/OrderService.php

namespace app\service;

use think\facade\Db;
use think\tracing\Tracer;

class OrderService
{
    /**
     * 获取用户订单列表
     * @param int $userId
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getUserOrders(int $userId, int $page = 1, int $limit = 20): array
    {
        $span = Tracer::startSpan('order.get_user_orders');
        $span->setTag('user.id', $userId);
        $span->setTag('pagination.page', $page);
        $span->setTag('pagination.limit', $limit);

        try {
            // 这些数据库查询会自动创建数据库Span
            $total = Db::table('orders')
                ->where('user_id', $userId)
                ->count();

            $orders = Db::table('orders')
                ->where('user_id', $userId)
                ->order('created_at', 'desc')
                ->limit($limit)
                ->page($page)
                ->select();

            // 获取订单商品信息
            $orderIds = array_column($orders, 'id');
            if (!empty($orderIds)) {
                $orderItems = Db::table('order_items')
                    ->whereIn('order_id', $orderIds)
                    ->select();

                // 组装订单商品数据
                $itemsMap = [];
                foreach ($orderItems as $item) {
                    $itemsMap[$item['order_id']][] = $item;
                }

                foreach ($orders as &$order) {
                    $order['items'] = $itemsMap[$order['id']] ?? [];
                }
            }

            $span->setTag('orders.count', count($orders));
            $span->setTag('orders.total', $total);

            return [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'data' => $orders,
            ];

        } finally {
            $span->finish();
        }
    }
}
```

### 5. 外部服务调用跟踪

#### HTTP客户端跟踪

```php
<?php
// app/service/ApiService.php

namespace app\service;

use think\tracing\Tracer;
use GuzzleHttp\Client;

class ApiService
{
    /**
     * 调用第三方API
     * @param string $url
     * @param array $data
     * @return array
     */
    public function callExternalApi(string $url, array $data = []): array
    {
        $span = Tracer::startSpan('http.external_api');
        $span->setTag('http.url', $url);
        $span->setTag('http.method', 'POST');

        try {
            $client = new Client(['timeout' => 30]);

            $response = $client->post($url, [
                'json' => $data,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'Swoole-App/1.0',
                    // 传递跟踪上下文
                    'X-Trace-Id' => Tracer::getTraceId(),
                    'X-Span-Id' => Tracer::getSpanId(),
                ]
            ]);

            $statusCode = $response->getStatusCode();
            $responseBody = $response->getBody()->getContents();

            $span->setTag('http.status_code', $statusCode);
            $span->setTag('response.size', strlen($responseBody));

            if ($statusCode >= 400) {
                $span->setTag('error', true);
                $span->setTag('error.message', "HTTP {$statusCode}");
            }

            return [
                'status_code' => $statusCode,
                'data' => json_decode($responseBody, true),
            ];

        } catch (\Exception $e) {
            $span->setTag('error', true);
            $span->setTag('error.message', $e->getMessage());
            throw $e;

        } finally {
            $span->finish();
        }
    }
}
```

### 6. 自定义跟踪中间件

#### 创建自定义跟踪中间件

```php
<?php
// app/middleware/CustomTraceMiddleware.php

namespace app\middleware;

use think\Request;
use think\Response;
use think\tracing\Tracer;
use Closure;

class CustomTraceMiddleware
{
    /**
     * 处理请求
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next)
    {
        // 创建根Span
        $span = Tracer::startSpan('http.request');

        // 设置请求信息
        $span->setTag('http.method', $request->method());
        $span->setTag('http.url', $request->url(true));
        $span->setTag('http.route', $request->route()->getRule());
        $span->setTag('user.ip', $request->ip());

        // 获取用户信息
        if ($request->has('user_id')) {
            $span->setTag('user.id', $request->param('user_id'));
        }

        try {
            // 执行请求
            $response = $next($request);

            // 设置响应信息
            $span->setTag('http.status_code', $response->getCode());

            if ($response->getCode() >= 400) {
                $span->setTag('error', true);
            }

            return $response;

        } catch (\Exception $e) {
            // 记录异常信息
            $span->setTag('error', true);
            $span->setTag('error.message', $e->getMessage());
            $span->setTag('error.class', get_class($e));

            throw $e;

        } finally {
            // 结束Span
            $span->finish();
        }
    }
}
```

## 示例代码

### 完整的业务流程跟踪示例

```php
<?php
// app/controller/Order.php

namespace app\controller;

use app\service\OrderService;
use app\service\UserService;
use app\service\PaymentService;
use think\Request;
use think\Response;
use think\tracing\Tracer;

class Order
{
    /**
     * 创建订单
     * @param Request $request
     * @return Response
     */
    public function create(Request $request)
    {
        $span = Tracer::startSpan('order.create_flow');

        try {
            $userId = $request->param('user_id');
            $products = $request->param('products', []);

            $span->setTag('user.id', $userId);
            $span->setTag('products.count', count($products));

            // 1. 验证用户
            $userService = app(UserService::class);
            $user = $userService->getUserInfo($userId);

            if (!$user) {
                throw new \Exception('用户不存在');
            }

            // 2. 创建订单
            $orderService = app(OrderService::class);
            $order = $orderService->createOrder($userId, $products);

            $span->setTag('order.id', $order['id']);
            $span->setTag('order.amount', $order['amount']);

            // 3. 处理支付
            $paymentService = app(PaymentService::class);
            $payment = $paymentService->processPayment($order['id'], $order['amount']);

            $span->setTag('payment.id', $payment['id']);
            $span->setTag('payment.status', $payment['status']);

            return json([
                'code' => 200,
                'msg' => '订单创建成功',
                'data' => [
                    'order_id' => $order['id'],
                    'payment_id' => $payment['id'],
                ]
            ]);

        } catch (\Exception $e) {
            $span->setTag('error', true);
            $span->setTag('error.message', $e->getMessage());

            return json([
                'code' => 500,
                'msg' => '订单创建失败: ' . $e->getMessage()
            ]);

        } finally {
            $span->finish();
        }
    }
}
```

## 注意事项

### 1. 性能影响

- 链路跟踪会增加一定的性能开销
- 在生产环境中合理设置采样率
- 避免在高频调用的方法中创建过多Span

### 2. 数据安全

- 不要在Span标签中记录敏感信息
- 对用户数据进行脱敏处理
- 注意跟踪数据的存储和传输安全

### 3. 存储管理

- 定期清理过期的跟踪数据
- 监控跟踪数据的存储空间
- 合理设置数据保留策略

### 4. 网络开销

- 跟踪数据的传输会占用网络带宽
- 在网络环境较差时考虑降低采样率
- 使用批量发送减少网络请求次数

### 5. 调试技巧

- 使用Jaeger或Zipkin的Web界面查看跟踪数据
- 通过跟踪ID关联相关的日志信息
- 分析慢查询和性能瓶颈

## 常见问题

### Q: 如何查看跟踪数据？
A: 访问Jaeger Web界面（默认http://localhost:16686）或Zipkin界面查看跟踪数据。

### Q: 跟踪数据过多怎么办？
A: 调整采样率配置，只跟踪部分请求，或设置更短的数据保留期。

### Q: 如何跟踪异步任务？
A: 在异步任务中手动创建Span，并传递父Span的上下文信息。

### Q: 跟踪对性能影响有多大？
A: 通常增加5-10%的性能开销，具体影响取决于跟踪的复杂度和采样率。
```
