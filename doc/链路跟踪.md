>基于topthink/think-tracing的链路跟踪

Rpc服务端跟踪需要在swoole.php配置文件，Rpc服务端定义 **middleware** 配置

```php

    'rpc' => [
        'server' => [
            'enable' => true,
            'middleware' => [
                \think\swoole\middleware\TraceRpcServer::class
            ],
        ],
        'client' => [
        ],
    ],

```

Rpc客户端跟踪需要在swoole.php配置文件，Rpc客户端定义 **middleware** 配置

```php

    'rpc' => [
        'server' => [
        ],
        'client' => [
            'rpc' => [
                'host' => '127.0.0.1',
                'port' => 9090,
                'max_active' => 20,
                'max_wait_time' => 1,
                'middleware' => [
                    \think\swoole\middleware\TraceRpcClient::class
                ],
            ],
        ],
    ],

```

跟踪每一条请求需要在 **app\middleware.php** 配置文件定义中间件

```php

return [
    \think\tracing\middleware\TraceRequests::class,
];

```
