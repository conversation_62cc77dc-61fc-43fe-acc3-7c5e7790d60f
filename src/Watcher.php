<?php
// +----------------------------------------------------------------------
// | Watcher
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole;

/**
 * @mixin \think\swoole\watcher\Driver
 */
class Watcher extends \think\Manager
{
    protected $namespace = '\\think\\swoole\\watcher\\driver\\';

    /**
     * 获取热更新配置
     * @param string $name
     * @param $default
     * @return array|mixed
     */
    protected function getConfig(string $name, $default = null)
    {
        return $this->app->config->get('swoole.hot_update.' . $name, $default);
    }

    /**
     * @param $name
     * @return \think\swoole\watcher\Driver
     */
    public function monitor($name = null)
    {
        return $this->driver($name);
    }

    /**
     * 获取驱动参数
     * @param $name
     * @return array
     */
    protected function resolveParams($name): array
    {
        return [
            [
                'directory' => array_filter($this->getConfig('include', []), function ($dir) {
                    return is_dir($dir);
                }),
                'exclude' => $this->getConfig('exclude', []),
                'name' => $this->getConfig('name', []),
            ],
        ];
    }

    /**
     * 获取默认驱动
     * @return string
     */
    public function getDefaultDriver()
    {
        return $this->getConfig('type', 'scan');
    }
}
