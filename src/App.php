<?php
// +----------------------------------------------------------------------
// | App
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole;

class App extends \think\App
{
    /**
     * @var bool
     */
    protected $inConsole = true;

    /**
     * @param bool $inConsole
     */
    public function setInConsole($inConsole = true)
    {
        $this->inConsole = $inConsole;
    }

    /**
     * 是否运行在命令行下
     * @return bool
     */
    public function runningInConsole(): bool
    {
        return $this->inConsole;
    }

    /**
     * 清理instances应用
     * @return bool
     */
    public function clearInstances()
    {
        $this->instances = [];
    }
}
