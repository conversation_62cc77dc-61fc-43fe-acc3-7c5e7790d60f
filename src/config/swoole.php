<?php
// +----------------------------------------------------------------------
// | 配置
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

use think\facade\App;
use think\facade\Env;

return [
    // http服务配置
    'http' => [
        'enable' => true,
        // 监听地址
        'host' => Env::get('swoole.host', '0.0.0.0'),
        // 监听端口
        'port' => Env::get('swoole.port', 8080),
        // 工作进程
        'worker_num' => 1,
        // 额外配置
        'options' => [],
    ],
    'rpc' => [
        'server' => [
            'enable' => false,
            'host' => Env::get('rpc.host', '0.0.0.0'),
            'port' => Env::get('rpc.port', 9090),
            'worker_num' => 1,
            'middleware' => [],
            'services' => [],
        ],
        'client' => [
        ],
    ],
    // 队列
    'queue' => [
        'enable' => false,
        'workers' => [],
    ],
    // websocket支持
    'websocket' => [
        'enable' => false,
        'route' => true,
        'handler' => \think\swoole\websocket\Handler::class,
        'ping_interval' => 25000,
        'ping_timeout' => 60000,
        'room' => [
            'type' => 'table',
            'table' => [
                'room_rows' => 8192,
                'room_size' => 2048,
                'client_rows' => 4096,
                'client_size' => 2048,
            ],
            'redis' => [
                'host' => '127.0.0.1',
                'port' => 6379,
                'max_active' => 3,
                'max_wait_time' => 5,
            ],
        ],
        'listen' => [],
        'subscribe' => [],
    ],
    // 热更新配置
    'hot_update' => [
        // 是否启用热更新
        'enable' => Env::get('swoole.hot_update', false),
        // 驱动类型，linux建议使用Find，否则使用Scan
        'type' => 'Find',
        // 监控文件类型
        'name' => ['*.php', '*.env'],
        // 监控目录
        'include' => [
            App::getRootPath() . 'view' . DIRECTORY_SEPARATOR,
            App::getBasePath(),
        ],
        'exclude' => [],
    ],
    // 连接池
    'pool' => [
        'db' => [
            'enable' => Env::get('pool_db.enable', true),
            'max_active' => Env::get('pool_db.max_active', 10),
            'max_wait_time' => Env::get('pool_db.max_wait_time', 1),
            'max_idle_time' => Env::get('pool_db.max_idle_time', 20),
        ],
        'cache' => [
            'enable' => Env::get('pool_cache.enable', true),
            'max_active' => Env::get('pool_cache.max_active', 20),
            'max_wait_time' => Env::get('pool_cache.max_wait_time', 1),
            'max_idle_time' => Env::get('pool_cache.max_idle_time', 20),
        ],
    ],
    // 进程间通信
    'ipc' => [
        'type' => 'unix_socket',
        'redis' => [
            'host' => '127.0.0.1',
            'port' => 6379,
            'max_active' => 3,
            'max_wait_time' => 5,
        ],
    ],
    // 锁
    'lock' => [
        'enable' => false,
        'type' => 'table',
        'redis' => [
            'host' => '127.0.0.1',
            'port' => 6379,
            'max_active' => 3,
            'max_wait_time' => 5,
        ],
    ],
    // 内存表
    'tables' => [],
    // 每个worker里需要预加载以共用的实例
    'concretes' => [],
    // 重置器
    'resetters' => [],
    // 每次请求前需要清空的实例
    'instances' => [],
    // 每次请求前需要重新执行的服务
    'services' => [],
];
