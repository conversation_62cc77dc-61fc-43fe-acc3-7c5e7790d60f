<?php
// +----------------------------------------------------------------------
// | Connector
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\pool;

use Smf\ConnectionPool\Connectors\ConnectorInterface;

class Connector implements ConnectorInterface
{
    protected $connector;

    protected $checker;

    public function __construct($connector)
    {
        $this->connector = $connector;
    }

    public function setChecker($checker)
    {
        $this->checker = $checker;
    }

    /**
     * 连接到指定的服务器并返回连接资源
     * @param array $config
     * @return false|mixed
     */
    public function connect(array $config)
    {
        return call_user_func($this->connector, $config);
    }

    /**
     * 断开连接并释放资源
     * @param mixed $connection
     * @return mixed|void
     */
    public function disconnect($connection)
    {

    }

    /**
     * 是否建立连接
     * @param mixed $connection
     * @return bool
     */
    public function isConnected($connection): bool
    {
        if ($this->checker) {
            return call_user_func($this->checker, $connection);
        }
        return true;
    }

    /**
     * 重置连接
     * @param mixed $connection
     * @param array $config
     * @return mixed|void
     */
    public function reset($connection, array $config)
    {

    }

    /**
     * 验证连接
     * @param mixed $connection
     * @return bool
     */
    public function validate($connection): bool
    {
        return true;
    }
}
