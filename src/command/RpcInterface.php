<?php
// +----------------------------------------------------------------------
// | RpcInterface
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\command;

use Nette\PhpGenerator\ClassType;
use Nette\PhpGenerator\Dumper;
use Nette\PhpGenerator\Helpers;
use Nette\PhpGenerator\PhpFile;
use think\console\Command;
use think\helper\Arr;
use think\swoole\contract\rpc\ParserInterface;
use think\swoole\rpc\client\Gateway;
use think\swoole\rpc\client\Service;
use think\swoole\rpc\JsonParser;
use function Swoole\Coroutine\run;

class RpcInterface extends Command
{
    /**
     * 配置指令
     * @return void
     */
    public function configure()
    {
        $this->setName('swoole:rpcinterface')
            ->setDescription('生成Rpc服务接口');
    }

    /**
     * 执行指令
     * 目的是根据客户端配置获取对应服务端的接口定义，并生成rpc.php文件
     */
    public function handle()
    {
        run(function () {
            $file = new PhpFile;
            $file->addComment('This file is auto-generated.');
            $file->setStrictTypes();
            $services = [];
            $clients = $this->app->config->get('swoole.rpc.client', []);
            foreach ($clients as $name => $config) {
                $parserClass = Arr::get($config, 'parser', JsonParser::class);
                /**
                 * @var ParserInterface $parser
                 */
                $parser = new $parserClass;
                $gateway = new Gateway($config, $parser, Arr::get($config, 'tries', 2));
                $result = $gateway->getServices();
                $namespace = $file->addNamespace("rpc\\contract\\{$name}");
                $namespace->addUse(Service::class);
                foreach ($result as $interface => $methods) {
                    $services[$name][] = $namespace->getName() . "\\{$interface}";
                    $class = $namespace->addInterface($interface);
                    $class->addExtend(Service::class);
                    foreach ($methods as $methodName => ['parameters' => $parameters, 'returnType' => $returnType, 'comment' => $comment]) {
                        $method = $class->addMethod($methodName)
                            ->setVisibility(ClassType::VISIBILITY_PUBLIC)
                            ->setComment(Helpers::unformatDocComment($comment))
                            ->setReturnType($returnType);
                        foreach ($parameters as $parameter) {
                            if ($parameter['type'] && (class_exists($parameter['type']) || interface_exists($parameter['type']))) {
                                $namespace->addUse($parameter['type']);
                            }
                            $param = $method->addParameter($parameter['name'])
                                ->setType($parameter['type']);
                            if (array_key_exists('default', $parameter)) {
                                $param->setDefaultValue($parameter['default']);
                            }
                            if (array_key_exists('nullable', $parameter)) {
                                $param->setNullable();
                            }
                        }
                    }
                }
            }
            $dumper = new Dumper();
            $services = 'return ' . $dumper->dump($services) . ';';
            file_put_contents($this->app->getBasePath() . 'rpc.php', $file . $services);
            $this->output->writeln('<info>Succeed!</info>');
        }
        );
    }
}
