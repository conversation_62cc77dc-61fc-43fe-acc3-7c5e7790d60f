<?php
// +----------------------------------------------------------------------
// | Server
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\command;

use think\console\Command;
use think\console\input\Option;
use think\swoole\Manager;

class Server extends Command
{

    /**
     * 配置指令
     * @return void
     */
    public function configure()
    {
        $this->setName('swoole')
            ->addOption('env', 'E', Option::VALUE_REQUIRED, '环境名称', '')
            ->setDescription('启动 Swoole 服务');
    }

    /**
     * 执行指令
     * @throws \Exception
     */
    public function handle(Manager $manager)
    {
        //检查环境
        $this->checkEnvironment();

        $this->output->writeln('启动swoole服务器...');

        if ($manager->getConfig('http.enable', false)) {
            $host = $manager->getConfig('http.host');
            $port = $manager->getConfig('http.port');

            $this->output->writeln("Swoole Http服务器已启动: <http://{$host}:{$port}>");
        }

        if ($manager->getConfig('rpc.server.enable', false)) {
            $host = $manager->getConfig('rpc.server.host');
            $port = $manager->getConfig('rpc.server.port');

            $this->output->writeln("Swoole Rpc服务器已启动: <tcp://{$host}:{$port}>");
        }

        $this->output->writeln('退出可以按 <info>`CTRL-C`</info>');

        $envName = $this->input->getOption('env');
        $manager->start($envName);
    }

    /**
     * 检查环境
     * @return void
     */
    protected function checkEnvironment()
    {
        if (!extension_loaded('swoole')) {
            $this->output->error('没有安装swoole扩展');
            exit(1);
        }
        if (!version_compare(swoole_version(), '4.6.0', 'ge')) {
            $this->output->error('您的Swoole版本必须高于“4.6.0”');
            exit(1);
        }
    }
}
