<?php
// +----------------------------------------------------------------------
// | ResetterContract 定义重置接口
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\contract;

use think\App;
use think\swoole\Sandbox;

interface ResetterInterface
{
    /**
     * "handle" function for resetting app.
     * @param App $app app对象
     * @param Sandbox $sandbox 沙箱对象
     */
    public function handle(App $app, Sandbox $sandbox);
}
