<?php
// +----------------------------------------------------------------------
// | HandlerInterface
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\contract\websocket;

use Swoole\WebSocket\Frame;
use think\Request;

interface HandlerInterface
{
    /**
     * 监听 onOpen
     * @param Request $request
     */
    public function onOpen(Request $request);

    /**
     * 监听 onMessage
     * @param Frame $frame
     */
    public function onMessage(Frame $frame);

    /**
     * 监听 onClose
     */
    public function onClose();

    /**
     * 编码
     * @param $message
     * @return mixed
     */
    public function encodeMessage($message);

}
