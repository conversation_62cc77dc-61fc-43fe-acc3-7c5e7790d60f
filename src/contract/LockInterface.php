<?php
// +----------------------------------------------------------------------
// | LockInterface
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\contract;

interface LockInterface
{
    /**
     * 准备
     * @return void
     */
    public function prepare();

    /**
     * 加锁
     * @param string $name
     * @param int $expire
     * @return true
     */
    public function lock($name, $expire = 60);

    /**
     * 解锁
     * @param string $name
     * @return void
     */
    public function unlock($name);
}
