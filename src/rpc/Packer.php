<?php
// +----------------------------------------------------------------------
// | Packer
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\rpc;

use RuntimeException;
use think\swoole\packet\Buffer;
use think\swoole\packet\File;

class Packer
{
    public const HEADER_SIZE = 8;
    public const HEADER_STRUCT = 'Nlength/Ntype';
    public const HEADER_PACK = 'NN';

    public const TYPE_BUFFER = 0;
    public const TYPE_FILE = 1;

    /**
     * 打包
     * @param $data
     * @param int $type
     * @return string
     */
    public static function pack($data, $type = self::TYPE_BUFFER)
    {
        return pack(self::HEADER_PACK, strlen($data), $type) . $data;
    }

    /**
     * 解包
     * @param $data
     * @return array<Buffer|File|string>
     */
    public static function unpack($data)
    {
        $header = unpack(self::HEADER_STRUCT, substr($data, 0, self::HEADER_SIZE));
        if ($header === false) {
            throw new RuntimeException('Invalid Header');
        }

        switch ($header['type']) {
            case Packer::TYPE_BUFFER:
                $handler = new Buffer($header['length']);
                break;
            case Packer::TYPE_FILE:
                $handler = new File($header['length']);
                break;
            default:
                throw new RuntimeException("unsupported data type: [{$header['type']}");
        }

        $data = substr($data, self::HEADER_SIZE);

        return [$handler, $data];
    }
}
