<?php
// +----------------------------------------------------------------------
// | Proxy 自动代理方式进行rpc调用
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\rpc\client;

use InvalidArgumentException;
use Nette\PhpGenerator\Factory;
use Nette\PhpGenerator\PhpNamespace;
use ReflectionClass;
use think\App;
use think\swoole\Middleware;
use think\swoole\rpc\Protocol;

abstract class Proxy implements Service
{

    /**
     * 接口名称
     * @var string
     */
    protected $interface;

    /**
     * @var Gateway
     */
    protected $gateway;

    /**
     * @var App
     */
    protected $app;

    /**
     * @var array
     */
    protected $middleware = [];

    /**
     * 上下文
     * @var array
     */
    protected $context = [];

    /**
     * Proxy constructor.
     * @param App $app
     * @param Gateway $gateway
     * @param $middleware
     */
    final public function __construct(App $app, Gateway $gateway, $middleware)
    {
        $this->app = $app;
        $this->gateway = $gateway;
        $this->middleware = $middleware;
    }

    /**
     * 代理方式调用rpc接口服务
     * @param string $method 接口
     * @param array|string $params 参数
     * @return mixed
     * @throws \think\swoole\exception\RpcResponseException
     */
    final protected function proxyCall($method, $params)
    {
        $protocol = Protocol::make($this->interface, $method, $params, $this->context);
        //执行rpc中间件
        return Middleware::make($this->app, $this->middleware)
            ->pipeline()
            ->send($protocol)
            ->then(function (Protocol $protocol) {
                return $this->gateway->call($protocol);
            });
    }

    /**
     * 设置上下文
     * @param $context
     * @return $this
     */
    final public function withContext($context): self
    {
        $this->context = $context;
        return $this;
    }

    /**
     * @param $client
     * @param $interface
     * @return string
     * @throws \ReflectionException
     */
    final public static function getClassName($client, $interface)
    {
        if (!interface_exists($interface)) {
            throw new InvalidArgumentException(
                sprintf('%s must be exist interface!', $interface)
            );
        }
        $proxyName = class_basename($interface) . "Service";
        $className = "rpc\\service\\{$client}\\{$proxyName}";
        if (!class_exists($className, false)) {
            $namespace = new PhpNamespace("rpc\\service\\{$client}");
            $namespace->addUse(Proxy::class);
            $namespace->addUse($interface);

            $class = $namespace->addClass($proxyName);

            $class->setExtends(Proxy::class);
            $class->addImplement($interface);
            $class->addProperty('interface', class_basename($interface));

            $reflection = new ReflectionClass($interface);

            foreach ($reflection->getMethods() as $methodRef) {
                if ($methodRef->getDeclaringClass()->name == Service::class) {
                    continue;
                }
                $method = (new Factory)->fromMethodReflection($methodRef);
                $body = "\$this->proxyCall('{$methodRef->getName()}', func_get_args());";
                if ($method->getReturnType() != 'void') {
                    $body = "return {$body}";
                }
                $method->setBody($body);
                $class->addMember($method);
            }

            eval($namespace);
        }
        return $className;
    }
}
