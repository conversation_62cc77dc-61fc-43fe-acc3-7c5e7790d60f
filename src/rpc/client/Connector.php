<?php
// +----------------------------------------------------------------------
// | Connector
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\rpc\client;

use Generator;

interface Connector
{
    /**
     * @param Generator|string $data
     * @param callable $decoder
     * @return string
     */
    public function sendAndRecv($data, callable $decoder);
}
