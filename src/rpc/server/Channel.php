<?php
// +----------------------------------------------------------------------
// | Channel
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\rpc\server;

use Swoole\Coroutine;
use think\swoole\packet\Buffer;
use think\swoole\packet\File;

class Channel
{
    /**
     * @var
     */
    protected $header;

    /**
     * @var \Swoole\Coroutine\Channel
     */
    protected $queue;

    /**
     * Channel constructor.
     * @param $handler
     */
    public function __construct($handler)
    {
        $this->queue = new Coroutine\Channel(1);
        Coroutine::create(
            function () use ($handler) {
                $this->queue->push($handler);
            }
        );
    }

    /**
     * @return File|Buffer
     */
    public function pop()
    {
        return $this->queue->pop();
    }

    /**
     * @param $handle
     * @return mixed
     */
    public function push($handle)
    {
        return $this->queue->push($handle);
    }

    /**
     * @return mixed
     */
    public function close()
    {
        return $this->queue->close();
    }
}
