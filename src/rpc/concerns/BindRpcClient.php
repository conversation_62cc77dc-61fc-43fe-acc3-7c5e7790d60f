<?php
// +----------------------------------------------------------------------
// | BindRpcClient 绑定rpc
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\rpc\concerns;

use Generator;
use Swoole\Client;
use Swoole\Coroutine;
use think\Exception;
use think\facade\App;
use think\facade\Config;
use think\helper\Arr;
use think\swoole\contract\rpc\ParserInterface;
use think\swoole\exception\RpcClientException;
use think\swoole\rpc\client\Connector;
use think\swoole\rpc\client\Gateway;
use think\swoole\rpc\client\Proxy;
use think\swoole\rpc\File;
use think\swoole\rpc\JsonParser;
use think\swoole\rpc\Packer;
use think\swoole\rpc\Protocol;

trait BindRpcClient
{
    /**
     * Rpc接口列表
     * @var array
     */
    protected array $rpcServices = [];

    /**
     * 准备RPC客户端
     * @return void
     */
    protected function prepareRpcClient(): void
    {
        //引入rpc接口文件
        if (file_exists($rpc = App::getBasePath() . 'rpc.php')) {
            $rpcServices = (array)include $rpc;
            $this->rpcServices = array_merge($rpcServices, $this->rpcServices);
        }
        $this->bindRpcClientPool();
    }

    /**
     * 绑定rpc客户端
     * @return void
     */
    protected function bindRpcClientPool(): void
    {
        try {
            foreach ($this->rpcServices as $name => $abstracts) {
                $parserClass = $this->getConfig("rpc.client.{$name}.parser", JsonParser::class);
                /**
                 * @var ParserInterface $parser
                 */
                $parser = App::make($parserClass);
                $gateway = new Gateway($this->createRpcConnector($name), $parser);
                $middleware = $this->getConfig("rpc.client.{$name}.middleware", []);
                foreach ($abstracts as $abstract) {
                    App::bind(
                        $abstract,
                        function () use ($middleware, $gateway, $name, $abstract) {
                            return App::invokeClass(Proxy::getClassName($name, $abstract), [$gateway, $middleware]);
                        }
                    );
                }
            }
        } catch (\Exception | \Throwable $e) {
        }
    }

    /**
     * 获取配置
     * @param string $name
     * @param null $default
     * @return mixed
     */
    private function getConfig(string $name, $default = null)
    {
        return Config::get("swoole.{$name}", $default);
    }

    /**
     * 创建连接器
     * @param string $name
     * @return Connector|object
     * @throws Exception
     */
    private function createRpcConnector($name): object
    {
        $config = $this->getConfig("rpc.client.{$name}", null);
        if (empty($config)) {
            throw new Exception('Rpc客户端配置信息错误');
        }
        return new class($config) implements Connector {

            /**
             * 客户端
             * @var Client|Coroutine\Client
             */
            protected $client;

            /**
             * 配置
             * @var array
             */
            protected $config;

            /**
             * constructor.
             * @param array $config
             */
            public function __construct(array $config)
            {
                $this->config = $config;
            }

            /**
             * 接收
             * @param Client|Coroutine\Client $client
             * @param callable $decoder
             * @return File|null|void
             * @throws RpcClientException
             */
            protected function recv($client, callable $decoder)
            {
                $handler = null;
                $file = null;
                while ($data = $client->recv()) {
                    begin:
                    if (empty($handler)) {
                        [$handler, $data] = Packer::unpack($data);
                    }
                    $response = $handler->write($data);
                    if (!empty($response)) {
                        $handler = null;
                        if ($response instanceof File) {
                            $file = $response;
                        } else {
                            $result = $decoder($response);
                            if ($result === Protocol::FILE) {
                                $result = $file;
                            }
                            return $result;
                        }
                    }
                    if (!empty($data)) {
                        goto begin;
                    }
                }
                if ($data === '') {
                    throw new RpcClientException('Connection is closed. ' . $client->errMsg ?? $client->errCode, $client->errCode);
                }
                if ($data === false) {
                    throw new RpcClientException('Error receiving data, errno=' . $client->errCode . ' errmsg=' . swoole_strerror($client->errCode), $client->errCode);
                }
            }

            /**
             * 发送并接收
             * @param $data
             * @param callable $decoder
             * @return mixed
             */
            public function sendAndRecv($data, callable $decoder)
            {
                if (!$data instanceof Generator) {
                    $data = [$data];
                }
                try {
                    $client = $this->getClient();
                    foreach ($data as $string) {
                        if (!empty($string)) {
                            if ($client->send($string) === false) {
                                throw new RpcClientException('Send data failed. ' . $client->errMsg ?? $client->errCode, $client->errCode);
                            }
                        }
                    }
                    return $this->recv($client, $decoder);
                } catch (\Exception $e) {
                    $client->close();
                    throw $e;
                }
            }

            /**
             * 连接是否可用
             * @return bool
             */
            protected function isConnected(): bool
            {
                $is = $this->client && $this->client->isConnected();
                if (Coroutine::getCid() > -1) {
                    return $is && $this->client->peek() !== '';
                }
                return $is;
            }

            /**
             * 获取客户端
             * @return Client
             */
            protected function getClient()
            {
                if (!$this->isConnected()) {
                    $class = Coroutine::getCid() > -1 ? Coroutine\Client::class : Client::class;
                    $client = new $class(SWOOLE_SOCK_TCP);
                    $config = $this->config;
                    $host = Arr::pull($config, 'host');
                    $port = Arr::pull($config, 'port');
                    $timeout = Arr::pull($config, 'timeout', 5);
                    $client->set($config);
                    if (!$client->connect($host, $port, $timeout)) {
                        throw new RpcClientException(
                            sprintf('Connect failed host=%s port=%d', $host, $port)
                        );
                    }
                    $this->client = $client;
                }
                return $this->client;
            }
        };
    }
}
