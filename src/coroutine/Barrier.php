<?php
// +----------------------------------------------------------------------
// | Barrier.php 隔离
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\coroutine;

use Swoole\Coroutine;

class Barrier
{
    /**
     * @param callable $func
     * @param ...$params
     * @return void
     */
    public static function run(callable $func, ...$params)
    {
        $channel = new Coroutine\Channel(1);

        Coroutine::create(function (...$params) use ($channel, $func) {
            Coroutine::defer(function () use ($channel) {
                $channel->close();
            });

            call_user_func_array($func, $params);
        }, ...$params);

        $channel->pop();
    }
}
