<?php
// +----------------------------------------------------------------------
// | Table 基于swoole内存表
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\lock;

use Swoole\Table as SwooleTable;
use think\swoole\contract\LockInterface;

class Table implements LockInterface
{
    /**
     * @var SwooleTable
     */
    protected $locks;

    /**
     * 准备
     * @return void
     */
    public function prepare()
    {
        $this->locks = new SwooleTable(1024);
        $this->locks->column('time', SwooleTable::TYPE_INT);
        $this->locks->create();
    }

    /**
     * 加锁
     * @param string $name
     * @param int $expire
     * @return true
     */
    public function lock($name, $expire = 60)
    {
        $time = time();
        while (true) {
            $lock = $this->locks->get($name);
            if (!$lock || $lock['time'] <= $time - $expire) {
                $this->locks->set($name, ['time' => time()]);
                return true;
            } else {
                usleep(500);
                continue;
            }
        }
    }

    /**
     * 解锁
     * @param string $name
     * @return void
     */
    public function unlock($name)
    {
        $this->locks->del($name);
    }
}
