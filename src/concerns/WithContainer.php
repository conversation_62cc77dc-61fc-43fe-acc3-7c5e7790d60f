<?php
// +----------------------------------------------------------------------
// | WithContainer
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\concerns;

use think\App;
use think\exception\Handle;
use Throwable;

trait WithContainer
{

    /**
     * think-swoole启动前的App对象
     * @var App
     */
    protected $container;

    /**
     * Manager constructor.
     * @param App $container
     */
    public function __construct(App $container)
    {
        $this->container = $container;
    }

    /**
     * 获取配置
     * @param string $name
     * @param mixed $default
     * @return mixed
     */
    public function getConfig(string $name, $default = null)
    {
        return $this->container->config->get("swoole.{$name}", $default);
    }

    /**
     * 监听事件
     * @param string $event
     * @param        $listener
     * @param bool $first
     */
    public function onEvent(string $event, $listener, bool $first = false): void
    {
        $this->container->event->listen("swoole.{$event}", $listener, $first);
    }

    /**
     * Log server error.
     * @param Throwable $e
     */
    public function logServerError(Throwable $e)
    {
        /** @var Handle $handle */
        $handle = $this->container->make(Handle::class);

        $handle->report($e);
    }

    /**
     * 触发事件
     * @param string $event
     * @param null $params
     */
    public function triggerEvent(string $event, $params = null): void
    {
        $this->container->event->trigger("swoole.{$event}", $params);
    }

    /**
     * 获取命令行下实例化的app对象
     * @return App
     */
    protected function getContainer()
    {
        return $this->container;
    }
}
