<?php
// +----------------------------------------------------------------------
// | InteractsWithLock
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\concerns;

use think\App;
use think\swoole\Lock;

/**
 * Trait InteractsWithLock
 * @package think\swoole\concerns
 * @property App $app
 * @property App $container
 */
trait InteractsWithLock
{
    /**
     * @var Lock
     */
    protected $lock;

    /**
     * 准备
     * @return void
     */
    protected function prepareLock()
    {
        if ($this->getConfig('lock.enable', false)) {
            $this->lock = $this->container->make(Lock::class);
            $this->lock->prepare();
            $this->onEvent('workerStart', function () {
                $this->app->instance(Lock::class, $this->lock);
            });
        }
    }
}
