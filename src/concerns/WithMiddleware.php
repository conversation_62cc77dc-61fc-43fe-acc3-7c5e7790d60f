<?php
// +----------------------------------------------------------------------
// | WithMiddleware
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\concerns;

trait WithMiddleware
{
    /**
     * 中间件
     * @var array
     */
    protected $middleware = [];

    /**
     * @param $middleware
     * @param ...$params
     * @return object
     */
    protected function middleware($middleware, ...$params)
    {
        $options = [];

        $this->middleware[] = [
            'middleware' => [$middleware, $params],
            'options' => &$options,
        ];

        return new class($options) {
            protected $options;

            public function __construct(array &$options)
            {
                $this->options = &$options;
            }

            public function only($methods)
            {
                $this->options['only'] = is_array($methods) ? $methods : func_get_args();
                return $this;
            }

            public function except($methods)
            {
                $this->options['except'] = is_array($methods) ? $methods : func_get_args();
                return $this;
            }
        };
    }
}
