<?php
// +----------------------------------------------------------------------
// | WithApplication
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\concerns;

use Closure;
use think\App;
use think\Container;
use think\Log;
use think\swoole\App as SwooleApp;
use think\swoole\Manager;
use think\swoole\pool\Cache;
use think\swoole\pool\Db;
use think\swoole\Sandbox;
use Throwable;

/**
 * Trait WithApplication
 * @package think\swoole\concerns
 * @property App $container
 */
trait WithApplication
{

    /**
     * 每个Worker进程里的
     * @var SwooleApp
     */
    protected $app;

    /**
     * 获取Worker进程里的app对象
     * @return SwooleApp
     */
    public function getApplication()
    {
        return $this->app;
    }

    /**
     * 在沙箱中执行
     * @param Closure $callable
     */
    public function runInSandbox(Closure $callable)
    {
        try {
            $this->getSandbox()->run($callable);
        } catch (Throwable $e) {
            $this->logServerError($e);
        }
    }

    /**
     * 应用准备
     * 在 Worker 进程 都会进行初始化
     * 在这些进程生命周期里，$this->app 对象是同一个，且和命令行下启动的app对象不是同一个，这个是新启动，一直到进程结束
     * @param string $envName 环境变量标识
     * @return void
     */
    protected function prepareApplication(string $envName)
    {
        if (!$this->app instanceof SwooleApp) {
            //实例化app对象
            $this->app = new SwooleApp($this->container->getRootPath());
            $this->app->setEnvName($envName);
            //绑定对象
            $this->app->bind(SwooleApp::class, App::class);
            $this->app->bind(Manager::class, $this);
            //绑定连接池
            if ($this->getConfig('pool.db.enable', true)) {
                $this->app->bind('db', Db::class);
                $this->app->resolving(Db::class, function (Db $db) {
                    $db->setLog(function ($type, $log) {
                        Container::getInstance()->make(Log::class)->log($type, $log);
                    });
                });
            }
            if ($this->getConfig('pool.cache.enable', true)) {
                $this->app->bind('cache', Cache::class);
            }
            //初始化应用
            $this->app->initialize();
            $this->app->instance('request', $this->container->request);
            $this->prepareConcretes();
        }
    }

    /**
     * 预加载
     * @return void
     */
    protected function prepareConcretes()
    {
        $defaultConcretes = ['db', 'cache', 'event'];
        $concretes = array_merge($defaultConcretes, $this->getConfig('concretes', []));
        foreach ($concretes as $concrete) {
            $this->app->make($concrete);
        }
    }

    /**
     * 获取沙箱
     * @return Sandbox
     */
    protected function getSandbox()
    {
        return $this->app->make(Sandbox::class);
    }
}
