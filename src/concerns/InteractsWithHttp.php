<?php
// +----------------------------------------------------------------------
// | InteractsWithHttp
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\concerns;

use Generator;
use Swoole\Coroutine;
use Swoole\Coroutine\Http\Server;
use Swoole\Http\Request;
use Swoole\Http\Response;
use Swoole\Http\Status;
use think\App;
use think\Cookie;
use think\Event;
use think\exception\Handle;
use think\helper\Arr;
use think\helper\Str;
use think\Http;
use think\response\View;
use think\swoole\App as SwooleApp;
use think\swoole\Http as SwooleHttp;
use think\swoole\response\File as FileResponse;
use think\swoole\response\Iterator as IteratorResponse;
use Throwable;
use function substr;

/**
 * Trait InteractsWithHttp
 * @package think\swoole\concerns
 * @property App $app
 * @property App $container
 */
trait InteractsWithHttp
{
    use InteractsWithWebsocket, ModifyProperty;

    /**
     * 创建http服务
     * @return void
     * @throws \ReflectionException
     */
    public function createHttpServer()
    {
        $this->preloadHttp();

        $host = $this->getConfig('http.host');
        $port = $this->getConfig('http.port');
        $options = $this->getConfig('http.options', []);

        $server = new Server($host, $port, false, true);
        $server->set($options);

        $server->handle('/', function (Request $req, Response $res) {
            if ($this->wsEnable && $this->isWebsocketRequest($req)) {
                $this->onHandShake($req, $res);
            } else {
                $this->onRequest($req, $res);
            }
        });

        $server->start();
    }

    /**
     * 预加载http服务
     * @return void
     * @throws \ReflectionException
     */
    protected function preloadHttp()
    {
        $http = $this->app->http;
        $this->app->invokeMethod([$http, 'loadMiddleware'], [], true);

        if ($this->app->config->get('app.with_route', true)) {
            $this->app->invokeMethod([$http, 'loadRoutes'], [], true);
            $route = clone $this->app->route;
            unset($this->app->route);

            $this->app->resolving(SwooleHttp::class, function ($http, App $app) use ($route) {
                $newRoute = clone $route;
                $this->modifyProperty($newRoute, $app);
                $app->instance('route', $newRoute);
            });
        }

        $middleware = clone $this->app->middleware;
        unset($this->app->middleware);

        $this->app->resolving(SwooleHttp::class, function ($http, App $app) use ($middleware) {
            $newMiddleware = clone $middleware;
            $this->modifyProperty($newMiddleware, $app);
            $app->instance('middleware', $newMiddleware);
        });

        unset($this->app->http);
        $this->app->bind(Http::class, SwooleHttp::class);
    }

    /**
     * 判断是否Websocket请求
     * @param Request $req
     * @return bool
     */
    protected function isWebsocketRequest(Request $req)
    {
        $header = $req->header;
        return strcasecmp(Arr::get($header, 'connection', ''), 'upgrade') === 0 &&
            strcasecmp(Arr::get($header, 'upgrade', ''), 'websocket') === 0;
    }

    /**
     * 准备http服务
     * @return void
     */
    protected function prepareHttp()
    {
        if ($this->getConfig('http.enable', true)) {
            $this->wsEnable = $this->getConfig('websocket.enable', false);
            if ($this->wsEnable) {
                $this->prepareWebsocket();
            }
            $workerNum = $this->getConfig('http.worker_num', swoole_cpu_num());
            $this->addBatchWorker($workerNum, [$this, 'createHttpServer'], 'http server');
        }
    }

    /**
     * 监听 "onRequest" 事件
     * @param Request $req http请求对象
     * @param Response $res http响应对象
     */
    public function onRequest($req, $res)
    {
        Coroutine::create(function () use ($req, $res) {
            $this->runInSandbox(function (Http $http, Event $event, SwooleApp $app) use ($req, $res) {
                $app->setInConsole(false);

                $request = $this->prepareRequest($req);

                try {
                    $response = $this->handleRequest($http, $request);
                    $this->prepareResponse($response);
                } catch (Throwable $e) {
                    $handle = $app->make(Handle::class);
                    $handle->report($e);
                    $response = $handle->render($request, $e);
                }

                $this->setCookie($res, $app->cookie);
                $this->sendResponse($res, $request, $response);

                $http->end($response);
            });
        });
    }

    /**
     * 渲染响应对象
     * @param \think\Response $response
     * @return void
     */
    protected function prepareResponse(\think\Response $response)
    {
        switch (true) {
            case $response instanceof View:
                $response->getContent();
                break;
        }
    }

    /**
     * 处理请求
     * @param Http $http
     * @param $request
     * @return \think\Response
     */
    protected function handleRequest(Http $http, $request)
    {
        $level = ob_get_level();
        ob_start();
        $response = $http->run($request);
        if (ob_get_length() > 0) {
            $content = $response->getContent();
            $response->content(ob_get_contents() . $content);
        }
        while (ob_get_level() > $level) {
            ob_end_clean();
        }
        return $response;
    }

    /**
     * 重新实例化请求对象 处理swoole请求数据
     * @param Request $req
     * @return \think\Request
     */
    protected function prepareRequest(Request $req)
    {
        $header = $req->header ?: [];
        $server = $req->server ?: [];
        foreach ($header as $key => $value) {
            $server['http_' . str_replace('-', '_', $key)] = $value;
        }

        /**
         * 重新实例化请求对象 处理swoole请求数据
         * @var \think\Request $request
         */
        $request = $this->app->make('request', [], true);
        return $request
            ->withHeader($header)
            ->withServer($server)
            ->withGet($req->get ?: [])
            ->withPost($req->post ?: [])
            ->withCookie($req->cookie ?: [])
            ->withFiles($this->getFiles($req))
            ->withInput($req->rawContent())
            ->setBaseUrl($req->server['request_uri'])
            ->setUrl($req->server['request_uri'] . (!empty($req->server['query_string']) ? '?' . $req->server['query_string'] : ''))
            ->setPathinfo(ltrim($req->server['path_info'], '/'));
    }

    /**
     * 获取上传文件
     * @param Request $req
     * @return array|array[]
     */
    protected function getFiles(Request $req)
    {
        if (empty($req->files)) {
            return [];
        }

        return array_map(function ($file) {
            if (!Arr::isAssoc($file)) {
                $files = [];
                foreach ($file as $f) {
                    $files['name'][] = $f['name'];
                    $files['type'][] = $f['type'];
                    $files['tmp_name'][] = $f['tmp_name'];
                    $files['error'][] = $f['error'];
                    $files['size'][] = $f['size'];
                }
                return $files;
            }
            return $file;
        }, $req->files);
    }

    /**
     * 设置cookie
     * @param Response $res
     * @param Cookie $cookie
     */
    protected function setCookie(Response $res, Cookie $cookie)
    {
        foreach ($cookie->getCookie() as $name => $val) {
            [$value, $expire, $option] = $val;

            $res->cookie($name, $value, $expire, $option['path'], $option['domain'], (bool)$option['secure'], (bool)$option['httponly'], $option['samesite']);
        }
    }

    /**
     * 设置请求头
     * @param Response $res
     * @param array $headers
     */
    protected function setHeader(Response $res, array $headers)
    {
        foreach ($headers as $key => $val) {
            $res->header($key, $val);
        }
    }

    /**
     * 设置状态
     * @param Response $res
     * @param $code
     */
    protected function setStatus(Response $res, $code)
    {
        $res->status($code, Status::getReasonPhrase($code));
    }

    /**
     * 响应请求
     * @param Response $res
     * @param \think\Response $response 框架响应对象
     * @param \think\Cookie $cookie
     */
    protected function sendResponse(Response $res, \think\Request $request, \think\Response $response)
    {
        switch (true) {
            case $response instanceof IteratorResponse:
                $this->sendIterator($res, $response);
                break;
            case $response instanceof FileResponse:
                $this->sendFile($res, $request, $response);
                break;
            default:
                $this->sendContent($res, $response);
        }
    }

    /**
     * @param Response $res
     * @param IteratorResponse $response
     * @return void
     * @throws \Exception
     */
    protected function sendIterator(Response $res, IteratorResponse $response)
    {
        $this->setHeader($res, $response->getHeader());
        $this->setStatus($res, $response->getCode());

        $iterator = $response->getIterator();

        if ($iterator instanceof Generator) {
            while ($iterator->valid()) {
                $content   = $iterator->current();
                $connected = $res->write($content);
                $iterator->send($connected);
            }
        } else {
            foreach ($iterator as $content) {
                $res->write($content);
            }
        }

        $res->end();
    }

    /**
     * 发送文件
     * @param Response $res
     * @param \think\Request $request
     * @param FileResponse $response
     */
    protected function sendFile(Response $res, \think\Request $request, FileResponse $response)
    {
        $header = $response->getHeader();
        $code = $response->getCode();
        $file = $response->getFile();

        $ifNoneMatch = $request->header('If-None-Match');
        $ifRange = $request->header('If-Range');
        $eTag = $response->getHeader('ETag');
        $lastModified = $response->getHeader('Last-Modified');

        $fileSize = $file->getSize();
        $offset = 0;
        $length = -1;

        if ($ifNoneMatch == $eTag) {
            $code = 304;
            unset($header['Content-Length']);
        } elseif (!$ifRange || $ifRange === $eTag || $ifRange === $lastModified) {
            $range = $request->header('Range', '');
            if (Str::startsWith($range, 'bytes=')) {
                [$start, $end] = explode('-', substr($range, 6), 2) + [0];

                $end = ('' === $end) ? $fileSize - 1 : (int)$end;

                if ('' === $start) {
                    $start = $fileSize - $end;
                    $end = $fileSize - 1;
                } else {
                    $start = (int)$start;
                }

                if ($start <= $end) {
                    $end = min($end, $fileSize - 1);
                    if ($start < 0 || $start > $end) {
                        $code = 416;
                        $header = array_merge($header, [
                            'Content-Range' => sprintf('bytes */%s', $fileSize),
                        ]);
                    } elseif ($end - $start < $fileSize - 1) {
                        $length = $end < $fileSize ? $end - $start + 1 : -1;
                        $offset = $start;
                        $code = 206;
                        $header = array_merge($header, [
                            'Content-Range' => sprintf('bytes %s-%s/%s', $start, $end, $fileSize),
                            'Content-Length' => $end - $start + 1,
                        ]);
                    }
                }
            }
        }

        $this->setStatus($res, $code);
        $this->setHeader($res, $header);

        if ($code >= 200 && $code < 300 && $length !== 0) {
            $res->sendfile($file->getPathname(), $offset, $length);
        } else {
            $res->end();
        }
    }

    /**
     * 发送内容
     * @param Response $res
     * @param \think\Response $response
     */
    protected function sendContent(Response $res, \think\Response $response)
    {
        $this->setStatus($res, $response->getCode());
        $this->setHeader($res, $response->getHeader());

        $content = $response->getContent();
        if ($content) {
            $contentSize = strlen($content);
            $chunkSize = 8192;

            if ($contentSize > $chunkSize) {
                $sendSize = 0;
                do {
                    if (!$res->write(substr($content, $sendSize, $chunkSize))) {
                        break;
                    }
                } while (($sendSize += $chunkSize) < $contentSize);
            } else {
                $res->write($content);
            }
        }
        $res->end();
    }
}
