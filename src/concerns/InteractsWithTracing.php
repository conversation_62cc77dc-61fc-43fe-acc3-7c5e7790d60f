<?php
// +----------------------------------------------------------------------
// | InteractsWithTracing 链路追踪
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\concerns;

use Smf\ConnectionPool\ConnectionPool;
use Smf\ConnectionPool\Connectors\PhpRedisConnector;
use think\helper\Arr;
use think\swoole\Pool;
use think\tracing\reporter\RedisReporter;
use think\tracing\Tracer;

/**
 * 链路追踪上报进程
 */
trait InteractsWithTracing
{
    /**
     * 准备链路跟踪
     * @return void
     */
    protected function prepareTracing()
    {
        // 判断是否有安装链路跟踪扩展
        if (class_exists(Tracer::class)) {
            $tracers = $this->container->config->get('tracing.tracers');
            $hasAsync = false;
            foreach ($tracers as $name => $tracer) {
                if (Arr::get($tracer, 'async', false)) {
                    $this->addWorker(function () use ($name) {
                        $tracer = $this->app->make(Tracer::class)->tracer($name);

                        $tracer->report();
                    }, "tracing [{$name}]");
                    $hasAsync = true;
                }
            }

            if ($hasAsync) {
                $this->onEvent('workerStart', function () {
                    $this->bindTracingRedisPool();
                    $this->bindTracingRedisReporter();
                });
            }
        }
    }

    /**
     * @return void
     */
    protected function bindTracingRedisReporter()
    {
        $this->getApplication()->bind(RedisReporter::class, function ($name) {
            $pool = $this->getPools()->get("tracing.redis");
            $redis = new class($pool) {

                protected $pool;

                public function __construct($pool)
                {
                    $this->pool = $pool;
                }

                public function __call($name, $arguments)
                {
                    $client = $this->pool->borrow();
                    try {
                        return call_user_func_array([$client, $name], $arguments);
                    } finally {
                        $this->pool->return($client);
                    }
                }
            };
            return new RedisReporter($name, $redis);
        });
    }

    /**
     * 连接池
     * @return void
     */
    protected function bindTracingRedisPool()
    {
        $config = $this->container->config->get('tracing.redis');
        $pool = new ConnectionPool(
            Pool::pullPoolConfig($config),
            new PhpRedisConnector(),
            $config
        );
        $this->getPools()->add("tracing.redis", $pool);
    }
}
