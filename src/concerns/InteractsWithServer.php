<?php
// +----------------------------------------------------------------------
// | InteractsWithServer
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\concerns;

use Swoole\Constant;
use Swoole\Coroutine;
use Swoole\Process;
use Swoole\Process\Pool;
use Swoole\Runtime;
use think\App;
use think\swoole\coroutine\Barrier;
use think\swoole\Ipc;
use think\swoole\message\ReloadMessage;
use think\swoole\Watcher;

/**
 * Trait InteractsWithServer
 * @package think\swoole\concerns
 * @property App $container
 */
trait InteractsWithServer
{

    /**
     * @var array
     */
    protected $startFuncMap = [];

    /**
     * 当前工作进程编号ID
     * @var int
     */
    protected $workerId;

    /**
     * 当前工作进程对象
     * @var Pool
     */
    protected $pool;

    /**
     * @var Ipc
     */
    protected $ipc;

    /**
     * 批量添加工作进程
     * @param int $workerNum
     * @param callable $func
     * @param null $name
     * @return $this
     */
    public function addBatchWorker(int $workerNum, callable $func, $name = null)
    {
        for ($i = 0; $i < $workerNum; $i++) {
            $this->addWorker($func, $name ? "{$name} #{$i}" : null);
        }
        return $this;
    }

    /**
     * 添加工作进程
     * @param callable $func
     * @param null $name
     * @return $this
     */
    public function addWorker(callable $func, $name = null): self
    {
        $this->startFuncMap[] = [$func, $name];
        return $this;
    }

    /**
     * 启动服务
     * @param string $envName 环境变量标识
     */
    public function start(string $envName): void
    {
        $this->setProcessName('manager process');

        //协程配置
        Coroutine::set($this->getConfig('coroutine', []));

        $this->initialize();
        $this->triggerEvent('init');

        //热更新
        if ($this->getConfig('hot_update.enable', false)) {
            $this->addHotUpdateProcess();
        }

        $workerNum = count($this->startFuncMap);

        $pool = $this->createPool($workerNum);

        $pool->on(Constant::EVENT_WORKER_START, function ($pool, $workerId) use ($envName) {

            Runtime::enableCoroutine();

            $this->pool = $pool;
            $this->workerId = $workerId;

            [$func, $name] = $this->startFuncMap[$workerId];

            if ($name) {
                $this->setProcessName($name);
            }

            $this->clearCache();
            $this->prepareApplication($envName);

            $this->ipc->listenMessage($workerId);
            Process::signal(SIGTERM, function () {
                $this->stopWorker();
            });

            $this->onEvent('message', function ($message) {
                if ($message instanceof ReloadMessage) {
                    $this->stopWorker();
                }
            });

            $this->triggerEvent(Constant::EVENT_WORKER_START, $name);

            $func($pool, $workerId);
        });

        $pool->start();
    }

    /**
     * 停止工作进程
     * @return void
     */
    protected function stopWorker()
    {
        $this->triggerEvent('beforeWorkerStop');
        $this->pool->getProcess()->exit();
    }

    /**
     * 获取当前工作进程ID
     * @return mixed
     */
    public function getWorkerId()
    {
        return $this->workerId;
    }

    /**
     * 获取当前工作进程池对象
     * @return Pool
     */
    public function getPool()
    {
        return $this->pool;
    }

    /**
     * 发送消息，工作进程间通信
     * @param $workerId
     * @param $message
     */
    public function sendMessage($workerId, $message)
    {
        $this->ipc->sendMessage($workerId, $message);
    }

    /**
     * 进程间通信准备
     * @param $workerNum
     * @return Pool
     */
    protected function createPool($workerNum)
    {
        $this->ipc = $this->container->make(Ipc::class);

        $pool = new Pool($workerNum, $this->ipc->getType(), 0, true);

        $this->ipc->prepare($pool);

        return $pool;
    }

    /**
     * 运行协程屏障
     * @param callable $func
     * @param mixed ...$params
     */
    public function runWithBarrier(callable $func, ...$params)
    {
        Barrier::run($func, ...$params);
    }

    /**
     * 热更新
     */
    protected function addHotUpdateProcess()
    {
        //热更新时关闭协程死锁检查
        Coroutine::set(
            [
                'enable_deadlock_check' => false,
            ]
        );

        $this->addWorker(function () {
            $watcher = $this->container->make(Watcher::class);
            $watcher->watch(function () {
                foreach ($this->startFuncMap as $workerId => $func) {
                    if ($workerId != $this->workerId) {
                        $this->sendMessage($workerId, new ReloadMessage);
                    }
                }
            });
        }, 'hot update');
    }

    /**
     * 清除apc、op缓存
     * @return void
     */
    protected function clearCache()
    {
        if (extension_loaded('apc')) {
            apc_clear_cache();
        }
        if (extension_loaded('Zend OPcache')) {
            opcache_reset();
        }
    }

    /**
     * 设置进程名
     * @param $process
     */
    protected function setProcessName($process)
    {
        $appName = $this->container->config->get('app.name', 'ThinkPHP');

        $name = sprintf('swoole: %s process for %s', $process, $appName);

        @cli_set_process_title($name);
    }
}
