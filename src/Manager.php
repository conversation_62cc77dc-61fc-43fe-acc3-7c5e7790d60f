<?php
// +----------------------------------------------------------------------
// | Manager
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole;

use think\swoole\concerns\InteractsWithHttp;
use think\swoole\concerns\InteractsWithLock;
use think\swoole\concerns\InteractsWithPools;
use think\swoole\concerns\InteractsWithQueue;
use think\swoole\concerns\InteractsWithRpcClient;
use think\swoole\concerns\InteractsWithRpcServer;
use think\swoole\concerns\InteractsWithServer;
use think\swoole\concerns\InteractsWithSwooleTable;
use think\swoole\concerns\InteractsWithTracing;
use think\swoole\concerns\WithApplication;
use think\swoole\concerns\WithContainer;

class Manager
{
    use InteractsWithServer,
        InteractsWithSwooleTable,
        InteractsWithHttp,
        InteractsWithPools,
        InteractsWithRpcClient,
        InteractsWithRpcServer,
        InteractsWithQueue,
        InteractsWithTracing,
        InteractsWithLock,
        WithContainer,
        WithApplication;

    /**
     * 初始化
     * @return void
     */
    protected function initialize(): void
    {
        // 内存表准备
        $this->prepareTables();
        // 连接池准备
        $this->preparePools();
        // 准备http服务
        $this->prepareHttp();
        // 准备rpc服务
        $this->prepareRpcServer();
        // 队列
        $this->prepareQueue();
        // 注册rpc客户端
        $this->prepareRpcClient();
        // 准备链路跟踪
        $this->prepareTracing();
        // 准备进程锁
        $this->prepareLock();
    }

}
