<?php
// +----------------------------------------------------------------------
// | Iterator.php
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\response;

use IteratorAggregate;
use think\Response;
use Traversable;

class Iterator extends Response implements IteratorAggregate
{
    protected $iterator;

    public function __construct(Traversable $iterator)
    {
        $this->iterator = $iterator;
    }

    public function getIterator(): Traversable
    {
        return $this->iterator;
    }
}
