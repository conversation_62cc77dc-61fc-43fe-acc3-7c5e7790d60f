<?php
// +----------------------------------------------------------------------
// | Scan
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\watcher\driver;

use Swoole\Timer;
use Symfony\Component\Finder\Finder;
use Symfony\Component\Finder\SplFileInfo;
use think\swoole\watcher\Driver;

class Scan extends Driver
{
    /**
     * @var Finder
     */
    protected $finder;

    /**
     * @var array
     */
    protected $files = [];

    /**
     * @var null
     */
    protected $timer = null;

    /**
     * @param $config
     */
    public function __construct($config)
    {
        $this->finder = new Finder();
        $this->finder
            ->files()
            ->name($config['name'])
            ->in($config['directory'])
            ->exclude($config['exclude']);
    }

    /**
     * @return array
     */
    protected function findFiles()
    {
        $files = [];
        /**
         * @var SplFileInfo $f
         */
        foreach ($this->finder as $f) {
            $files[$f->getRealpath()] = $f->getMTime();
        }
        return $files;
    }

    /**
     * 监控
     * @param callable $callback
     * @return mixed|void
     */
    public function watch(callable $callback)
    {
        $this->files = $this->findFiles();

        $this->timer = Timer::tick(2000, function () use ($callback) {

            $files = $this->findFiles();

            foreach ($files as $path => $time) {
                if (empty($this->files[$path]) || $this->files[$path] != $time) {
                    call_user_func($callback, [$path]);
                    break;
                }
            }

            $this->files = $files;
        });
    }

    public function stop()
    {
        if ($this->timer) {
            Timer::clear($this->timer);
        }
    }

}
