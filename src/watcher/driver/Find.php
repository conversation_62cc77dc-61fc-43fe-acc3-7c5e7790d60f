<?php
// +----------------------------------------------------------------------
// | Find 系统需要支持 find 命令
// | https://www.runoob.com/linux/linux-comm-find.html
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\watcher\driver;

use InvalidArgumentException;
use Swoole\Coroutine\System;
use Swoole\Timer;
use think\helper\Str;
use think\swoole\watcher\Driver;

class Find extends Driver
{
    /**
     * 监控文件类型
     * @var array
     */
    protected $name;

    /**
     * 监控目录
     * @var array
     */
    protected $directory;

    /**
     * 排查
     * @var array
     */
    protected $exclude;

    protected $timer = null;

    /**
     * @param $config
     */
    public function __construct($config)
    {
        $ret = System::exec('which find');
        if (empty($ret['output'])) {
            throw new InvalidArgumentException('find not exists.');
        }
        $ret = System::exec('find --help', true);
        if (Str::contains($ret['output'] ?? '', 'BusyBox')) {
            throw new InvalidArgumentException('find version not support.');
        }

        $this->directory = $config['directory'];
        $this->exclude = $config['exclude'];
        $this->name = $config['name'];
    }

    /**
     * 监控
     * @param callable $callback
     * @return void
     */
    public function watch(callable $callback)
    {
        $ms = 2000;
        $seconds = ceil(($ms + 1000) / 1000);
        $minutes = sprintf('-%.2f', $seconds / 60);

        $dest = implode(' ', $this->directory);

        $name = empty($this->name) ? '' : ' \( ' . join(' -o ', array_map(fn($v) => "-name \"{$v}\"", $this->name)) . ' \)';
        $notName = '';
        $notPath = '';
        if (!empty($this->exclude)) {
            $excludeDirs = $excludeFiles = [];
            foreach ($this->exclude as $directory) {
                $directory = rtrim($directory, '/');
                if (is_dir($directory)) {
                    $excludeDirs[] = $directory;
                } else {
                    $excludeFiles[] = $directory;
                }
            }

            if (!empty($excludeFiles)) {
                $notPath = ' -not \( ' . join(' -and ', array_map(fn($v) => "-name \"{$v}\"", $excludeFiles)) . ' \)';
            }

            if (!empty($excludeDirs)) {
                $notPath = ' -not \( ' . join(' -and ', array_map(fn($v) => "-path \"{$v}/*\"", $excludeDirs)) . ' \)';
            }
        }

        $command = "find {$dest}{$name}{$notName}{$notPath} -mmin {$minutes} -type f -print";

        $this->timer = Timer::tick($ms, function () use ($callback, $command) {
            $ret = System::exec($command);
            if ($ret['code'] === 0 && strlen($ret['output'])) {
                $stdout = trim($ret['output']);
                if (!empty($stdout)) {
                    $files = array_filter(explode("\n", $stdout));
                    call_user_func($callback, $files);
                }
            }
        });
    }

    public function stop()
    {
        if ($this->timer) {
            Timer::clear($this->timer);
        }
    }

}
