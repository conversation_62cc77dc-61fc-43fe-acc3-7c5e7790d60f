<?php
// +----------------------------------------------------------------------
// | Driver
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------
namespace think\swoole\watcher;

abstract class Driver
{
    abstract public function watch(callable $callback);

    abstract public function stop();
}
