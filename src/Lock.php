<?php
// +----------------------------------------------------------------------
// | Lock
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole;

use think\Manager;

/**
 * @mixin \think\swoole\lock\Table
 */
class Lock extends Manager
{
    /**
     * 命名空间
     * @var string
     */
    protected $namespace = "\\think\\swoole\\lock\\";

    /**
     * 获取驱动配置
     * @param string $name
     * @return array|mixed|string
     */
    protected function resolveConfig(string $name)
    {
        return $this->app->config->get("swoole.lock.{$name}", []);
    }

    /**
     * 默认驱动
     * @return string|null
     */
    public function getDefaultDriver()
    {
        return $this->app->config->get('swoole.lock.type', 'table');
    }
}
