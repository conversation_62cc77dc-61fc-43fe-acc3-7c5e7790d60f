<?php
// +----------------------------------------------------------------------
// | Driver.php
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\ipc;

use Swoole\Process\Pool;
use think\swoole\Manager;

abstract class Driver
{
    /**
     * @var array
     */
    protected $config;

    /**
     * @var Manager
     */
    protected $manager;

    /**
     * @var
     */
    protected $workerId;

    /**
     * @param Manager $manager
     * @param array $config
     */
    public function __construct(Manager $manager, array $config)
    {
        $this->manager = $manager;
        $this->config = $config;
    }

    /**
     * @param $workerId
     * @return void
     */
    public function listenMessage($workerId)
    {
        $this->workerId = $workerId;

        $this->subscribe();
    }

    /**
     * @param $workerId
     * @param $message
     * @return void
     */
    public function sendMessage($workerId, $message)
    {
        if ($workerId === $this->workerId) {
            $this->manager->triggerEvent('message', $message);
        } else {
            $this->publish($workerId, $message);
        }
    }

    /**
     * 通信类型
     * @return mixed
     */
    abstract public function getType();

    /**
     * 准备工作
     * @param Pool $pool
     * @return mixed
     */
    abstract public function prepare(Pool $pool);

    abstract public function subscribe();

    abstract public function publish($workerId, $message);
}
