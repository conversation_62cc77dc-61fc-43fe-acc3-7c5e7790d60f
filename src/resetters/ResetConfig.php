<?php
// +----------------------------------------------------------------------
// | ResetConfig 复制沙盒里的配置对象到容器config
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\resetters;

use think\App;
use think\swoole\contract\ResetterInterface;
use think\swoole\Sandbox;

class ResetConfig implements ResetterInterface
{

    /**
     * @param App $app
     * @param Sandbox $sandbox
     * @return App
     */
    public function handle(App $app, Sandbox $sandbox)
    {
        $app->instance('config', clone $sandbox->getConfig());
        return $app;
    }
}
