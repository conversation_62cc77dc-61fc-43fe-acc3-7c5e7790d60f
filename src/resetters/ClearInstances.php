<?php
// +----------------------------------------------------------------------
// | ClearInstances 清理容器中的对象实例
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace think\swoole\resetters;

use think\App;
use think\swoole\contract\ResetterInterface;
use think\swoole\Sandbox;

class ClearInstances implements ResetterInterface
{
    /**
     * @param App $app
     * @param Sandbox $sandbox
     * @return App
     */
    public function handle(App $app, Sandbox $sandbox)
    {
        $instances = ['log', 'session', 'view', 'response', 'cookie'];
        $instances = array_merge($instances, $sandbox->getConfig()->get('swoole.instances', []));
        foreach ($instances as $instance) {
            $app->delete($instance);
        }
        return $app;
    }
}
